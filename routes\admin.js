var express = require('express');
var router = express.Router();
const adminController = require('../controller/adminController');
const menuController = require('../controller/menuController');
const commonService = require('../services/commonService');

// Middleware kiểm tra đăng nhập cho tất cả routes admin
router.use(commonService.isAuthenticated);

// ==================== DASHBOARD ====================
router.get('/', adminController.index);

// ==================== TABLES MANAGEMENT ====================
router.get('/tables', adminController.tables);
router.get('/tables/data', adminController.getTablesData);
router.post('/tables', adminController.createTable);
router.get('/tables/:id', adminController.getTable);
router.put('/tables/:id', adminController.updateTable);
router.delete('/tables/:id', adminController.deleteTable);

// ==================== TABLE STRUCTURE MANAGEMENT ====================
router.get('/tables/:tableId/structure', adminController.tableStructure);
router.post('/tables/:tableId/structure', adminController.updateTableStructure);

// ==================== COLUMNS MANAGEMENT ====================
router.post('/tables/:tableId/columns', adminController.addColumn);
router.put('/columns/:id', adminController.updateColumn);
router.delete('/columns/:id', adminController.deleteColumn);

// ==================== RELATIONS MANAGEMENT ====================
router.post('/relations', adminController.createRelation);
router.delete('/relations/:id', adminController.deleteRelation);

// ==================== DYNAMIC CRUD OPERATIONS ====================
router.get('/tables/:tableId/data', adminController.tableData);
router.get('/tables/:tableId/data/api', adminController.getTableDataApi);
router.get('/tables/:tableId/records/:recordId', adminController.getRecord);
router.post('/tables/:tableId/records', adminController.createRecord);
router.put('/tables/:tableId/records/:recordId', adminController.updateRecord);
router.delete('/tables/:tableId/records/:recordId', adminController.deleteRecord);
router.get('/dropdown/:tableName', adminController.getDropdownData);
router.get('/tables/:tableId/relations/:columnName/dropdown', adminController.getRelationDropdownData);

// ==================== UTILITY ENDPOINTS ====================
router.post('/sync-tables', adminController.syncTables);

// ==================== MENU MANAGEMENT ====================
router.get('/menus', menuController.index);
router.get('/menus/data', menuController.getMenusData);
router.get('/menus/:id', menuController.getMenu);
router.post('/menus', menuController.createMenu);
router.put('/menus/:id', menuController.updateMenu);
router.delete('/menus/:id', menuController.deleteMenu);
router.post('/menus/sync-tables', menuController.syncTableMenus);
router.post('/menus/update-order', menuController.updateMenuOrder);
router.post('/menus/create-defaults', menuController.createDefaultMenus);

// ==================== MENU API ENDPOINTS ====================
router.get('/api/sidebar-menus', menuController.getSidebarMenus);
router.get('/api/parent-menus', menuController.getParentMenus);
router.get('/api/admin-tables', menuController.getAdminTables);

module.exports = router;
