const db = require('./config/database');
const bcrypt = require('bcryptjs');

async function fixLoginData() {
  try {
    console.log('🔧 Fixing login data after cascade delete...\n');

    // 1. Kết nối database
    console.log('1. Connecting to database...');
    db.connect('fix');
    console.log('✅ Database connected\n');

    // 2. <PERSON><PERSON>m tra bảng users
    console.log('2. Checking users table...');
    const users = await db.query('SELECT * FROM user');
    console.log(`📊 Found ${users.length} users in database`);
    
    if (users.length > 0) {
      console.log('👥 Current users:');
      users.forEach(user => {
        console.log(`   - ID: ${user.id}, Email: ${user.email}, Name: ${user.name || 'N/A'}`);
      });
    } else {
      console.log('⚠️ No users found in database!');
    }

    // 3. <PERSON><PERSON><PERSON> tra bảng roles
    console.log('\n3. Checking roles table...');
    const roles = await db.query('SELECT * FROM role');
    console.log(`📊 Found ${roles.length} roles in database`);
    
    if (roles.length > 0) {
      console.log('🎭 Current roles:');
      roles.forEach(role => {
        console.log(`   - ID: ${role.id}, Name: ${role.name}, Description: ${role.description || 'N/A'}`);
      });
    } else {
      console.log('⚠️ No roles found in database!');
    }

    // 4. Kiểm tra bảng role_user
    console.log('\n4. Checking role_user relationships...');
    const roleUsers = await db.query(`
      SELECT ru.*, u.email, r.name as role_name 
      FROM role_user ru 
      LEFT JOIN user u ON ru.user_id = u.id 
      LEFT JOIN role r ON ru.role_id = r.id
    `);
    console.log(`📊 Found ${roleUsers.length} role-user relationships`);
    
    if (roleUsers.length > 0) {
      console.log('🔗 Current role-user relationships:');
      roleUsers.forEach(ru => {
        console.log(`   - User: ${ru.email || 'Unknown'} → Role: ${ru.role_name || 'Unknown'}`);
      });
    } else {
      console.log('⚠️ No role-user relationships found!');
    }

    // 5. Tạo lại dữ liệu cần thiết nếu bị mất
    console.log('\n5. Creating missing essential data...');

    // Tạo role admin nếu chưa có
    let adminRole = roles.find(r => r.name === 'admin');
    if (!adminRole) {
      console.log('🎭 Creating admin role...');
      const result = await db.query(`
        INSERT INTO role (name, description, created_at, updated_at) 
        VALUES ('admin', 'Administrator role', NOW(), NOW())
      `);
      adminRole = { id: result.insertId, name: 'admin' };
      console.log(`✅ Admin role created with ID: ${adminRole.id}`);
    } else {
      console.log(`✅ Admin role exists with ID: ${adminRole.id}`);
    }

    // Tạo role user nếu chưa có
    let userRole = roles.find(r => r.name === 'user');
    if (!userRole) {
      console.log('🎭 Creating user role...');
      const result = await db.query(`
        INSERT INTO role (name, description, created_at, updated_at) 
        VALUES ('user', 'Regular user role', NOW(), NOW())
      `);
      userRole = { id: result.insertId, name: 'user' };
      console.log(`✅ User role created with ID: ${userRole.id}`);
    } else {
      console.log(`✅ User role exists with ID: ${userRole.id}`);
    }

    // Tạo admin user nếu chưa có
    let adminUser = users.find(u => u.email === '<EMAIL>');
    if (!adminUser) {
      console.log('👤 Creating admin user...');
      const hashedPassword = await bcrypt.hash('admin123', 10);
      const result = await db.query(`
        INSERT INTO user (name, email, password, email_verified_at, created_at, updated_at) 
        VALUES ('Admin User', '<EMAIL>', ?, NOW(), NOW(), NOW())
      `, [hashedPassword]);
      adminUser = { id: result.insertId, email: '<EMAIL>' };
      console.log(`✅ Admin user created with ID: ${adminUser.id}`);
      console.log(`   📧 Email: <EMAIL>`);
      console.log(`   🔑 Password: admin123`);
    } else {
      console.log(`✅ Admin user exists with ID: ${adminUser.id}`);
    }

    // Tạo relationship admin user - admin role nếu chưa có
    const adminRoleUser = roleUsers.find(ru => ru.user_id === adminUser.id && ru.role_id === adminRole.id);
    if (!adminRoleUser) {
      console.log('🔗 Creating admin user-role relationship...');
      await db.query(`
        INSERT INTO role_user (user_id, role_id, created_at, updated_at) 
        VALUES (?, ?, NOW(), NOW())
      `, [adminUser.id, adminRole.id]);
      console.log(`✅ Admin user-role relationship created`);
    } else {
      console.log(`✅ Admin user-role relationship exists`);
    }

    // 6. Tạo test user nếu cần
    console.log('\n6. Creating test user if needed...');
    let testUser = users.find(u => u.email === '<EMAIL>');
    if (!testUser) {
      console.log('👤 Creating test user...');
      const hashedPassword = await bcrypt.hash('test123', 10);
      const result = await db.query(`
        INSERT INTO user (name, email, password, email_verified_at, created_at, updated_at) 
        VALUES ('Test User', '<EMAIL>', ?, NOW(), NOW(), NOW())
      `, [hashedPassword]);
      testUser = { id: result.insertId, email: '<EMAIL>' };
      console.log(`✅ Test user created with ID: ${testUser.id}`);
      console.log(`   📧 Email: <EMAIL>`);
      console.log(`   🔑 Password: test123`);

      // Gán role user cho test user
      await db.query(`
        INSERT INTO role_user (user_id, role_id, created_at, updated_at) 
        VALUES (?, ?, NOW(), NOW())
      `, [testUser.id, userRole.id]);
      console.log(`✅ Test user-role relationship created`);
    } else {
      console.log(`✅ Test user exists with ID: ${testUser.id}`);
    }

    // 7. Kiểm tra lại sau khi fix
    console.log('\n7. Final verification...');
    const finalUsers = await db.query('SELECT COUNT(*) as count FROM user');
    const finalRoles = await db.query('SELECT COUNT(*) as count FROM role');
    const finalRoleUsers = await db.query('SELECT COUNT(*) as count FROM role_user');

    console.log(`📊 Final counts:`);
    console.log(`   - Users: ${finalUsers[0].count}`);
    console.log(`   - Roles: ${finalRoles[0].count}`);
    console.log(`   - Role-User relationships: ${finalRoleUsers[0].count}`);

    // 8. Hiển thị thông tin đăng nhập
    console.log('\n8. Login credentials available:');
    console.log('🔐 Admin Account:');
    console.log('   📧 Email: <EMAIL>');
    console.log('   🔑 Password: admin123');
    console.log('   🎭 Role: admin');
    
    console.log('\n🔐 Test Account:');
    console.log('   📧 Email: <EMAIL>');
    console.log('   🔑 Password: test123');
    console.log('   🎭 Role: user');

    console.log('\n✅ Login data has been fixed! You should now be able to login.');

  } catch (error) {
    console.error('❌ Error fixing login data:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    try {
      console.log('\n🔌 Closing database connection...');
      process.exit(0);
    } catch (error) {
      console.error('Error closing database connection:', error);
      process.exit(1);
    }
  }
}

// Chạy fix
fixLoginData();
