const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');
const schemaService = require('./schemaService');

const execAsync = promisify(exec);

class PrismaMigrationService {
  constructor() {
    this.migrationsDir = path.join(__dirname, '../prisma/migrations');
    this.schemaPath = path.join(__dirname, '../prisma/schema.prisma');
  }

  // Tạo migration cho bảng mới
  async createTableMigration(tableName, columns) {
    try {
      console.log(`Creating migration for table: ${tableName}`);

      // Tạo nội dung migration SQL
      const migrationSQL = this.generateCreateTableSQL(tableName, columns);
      
      // Tạo tên migration
      const timestamp = new Date().toISOString().replace(/[-:]/g, '').split('.')[0];
      const migrationName = `create_${tableName}_${timestamp}`;
      const migrationDir = path.join(this.migrationsDir, migrationName);
      
      // Tạo thư mục migration
      await fs.mkdir(migrationDir, { recursive: true });
      
      // Tạo file migration.sql
      const migrationFilePath = path.join(migrationDir, 'migration.sql');
      await fs.writeFile(migrationFilePath, migrationSQL);
      
      // Cập nhật schema.prisma
      await this.addModelToSchema(tableName, columns);
      
      // Tạo migration lock file nếu chưa có
      await this.ensureMigrationLock();
      
      console.log(`Migration created: ${migrationName}`);
      return { success: true, migrationName, migrationDir };
    } catch (error) {
      console.error('Error creating migration:', error);
      throw error;
    }
  }

  // Tạo migration để xóa bảng
  async dropTableMigration(tableName) {
    try {
      console.log(`Creating drop migration for table: ${tableName}`);

      // Tạo nội dung migration SQL
      const migrationSQL = `DROP TABLE IF EXISTS \`${tableName}\`;`;
      
      // Tạo tên migration
      const timestamp = new Date().toISOString().replace(/[-:]/g, '').split('.')[0];
      const migrationName = `drop_${tableName}_${timestamp}`;
      const migrationDir = path.join(this.migrationsDir, migrationName);
      
      // Tạo thư mục migration
      await fs.mkdir(migrationDir, { recursive: true });
      
      // Tạo file migration.sql
      const migrationFilePath = path.join(migrationDir, 'migration.sql');
      await fs.writeFile(migrationFilePath, migrationSQL);
      
      // Xóa model khỏi schema.prisma
      await this.removeModelFromSchema(tableName);
      
      // Tạo migration lock file nếu chưa có
      await this.ensureMigrationLock();
      
      console.log(`Drop migration created: ${migrationName}`);
      return { success: true, migrationName, migrationDir };
    } catch (error) {
      console.error('Error creating drop migration:', error);
      throw error;
    }
  }

  // Chạy migration
  async runMigration() {
    try {
      console.log('Running migrations...');
      
      const { stdout, stderr } = await execAsync('npx prisma migrate deploy', {
        cwd: path.join(__dirname, '..')
      });
      
      if (stderr) {
        console.warn('Migration warnings:', stderr);
      }
      
      console.log('Migration output:', stdout);
      return { success: true, output: stdout };
    } catch (error) {
      console.error('Error running migration:', error);
      throw error;
    }
  }

  // Tạo SQL để tạo bảng
  generateCreateTableSQL(tableName, columns) {
    const columnDefinitions = columns.map(col => {
      let definition = `\`${col.name}\` ${this.mapAdminTypeToMySQL(col.type)}`;
      
      if (col.length && (col.type === 'varchar' || col.type === 'int')) {
        definition += `(${col.length})`;
      }
      
      if (!col.is_nullable) {
        definition += ' NOT NULL';
      }
      
      if (col.is_primary) {
        definition += ' PRIMARY KEY';
      }
      
      if (col.is_auto_increment) {
        definition += ' AUTO_INCREMENT';
      }
      
      if (col.is_unique) {
        definition += ' UNIQUE';
      }
      
      if (col.default_value) {
        definition += ` DEFAULT ${col.default_value}`;
      }
      
      return definition;
    }).join(',\n  ');

    return `CREATE TABLE \`${tableName}\` (\n  ${columnDefinitions}\n);`;
  }

  // Map admin type sang MySQL type
  mapAdminTypeToMySQL(adminType) {
    const typeMap = {
      'int': 'INT',
      'bigint': 'BIGINT',
      'smallint': 'SMALLINT',
      'tinyint': 'TINYINT',
      'varchar': 'VARCHAR',
      'char': 'CHAR',
      'text': 'TEXT',
      'longtext': 'LONGTEXT',
      'mediumtext': 'MEDIUMTEXT',
      'tinytext': 'TINYTEXT',
      'datetime': 'DATETIME',
      'timestamp': 'TIMESTAMP',
      'date': 'DATE',
      'time': 'TIME',
      'year': 'YEAR',
      'boolean': 'BOOLEAN',
      'decimal': 'DECIMAL',
      'float': 'FLOAT',
      'double': 'DOUBLE',
      'json': 'JSON',
      'blob': 'BLOB',
      'longblob': 'LONGBLOB',
      'mediumblob': 'MEDIUMBLOB',
      'tinyblob': 'TINYBLOB',
      'enum': 'ENUM',
      'set': 'SET'
    };
    
    return typeMap[adminType] || 'VARCHAR';
  }

  // Thêm model vào schema.prisma
  async addModelToSchema(tableName, columns) {
    try {
      const schemaContent = await fs.readFile(this.schemaPath, 'utf8');
      
      // Tạo model definition
      const modelDefinition = this.generateModelDefinition(tableName, columns);
      
      // Thêm model vào cuối file (trước dòng cuối)
      const lines = schemaContent.split('\n');
      const insertIndex = lines.length - 1; // Trước dòng cuối
      
      lines.splice(insertIndex, 0, '', modelDefinition);
      
      await fs.writeFile(this.schemaPath, lines.join('\n'));
      console.log(`Model ${tableName} added to schema.prisma`);
    } catch (error) {
      console.error('Error adding model to schema:', error);
      throw error;
    }
  }

  // Xóa model khỏi schema.prisma
  async removeModelFromSchema(tableName) {
    try {
      const schemaContent = await fs.readFile(this.schemaPath, 'utf8');
      const lines = schemaContent.split('\n');
      
      // Tìm vị trí bắt đầu và kết thúc của model
      let startIndex = -1;
      let endIndex = -1;
      let braceCount = 0;
      let inModel = false;
      
      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim();
        
        if (line.startsWith(`model ${this.capitalizeFirst(tableName)}`)) {
          startIndex = i;
          inModel = true;
          braceCount = 0;
        }
        
        if (inModel) {
          if (line.includes('{')) braceCount++;
          if (line.includes('}')) braceCount--;
          
          if (braceCount === 0 && startIndex !== -1) {
            endIndex = i;
            break;
          }
        }
      }
      
      if (startIndex !== -1 && endIndex !== -1) {
        lines.splice(startIndex, endIndex - startIndex + 1);
        await fs.writeFile(this.schemaPath, lines.join('\n'));
        console.log(`Model ${tableName} removed from schema.prisma`);
      }
    } catch (error) {
      console.error('Error removing model from schema:', error);
      throw error;
    }
  }

  // Tạo model definition cho Prisma schema
  generateModelDefinition(tableName, columns) {
    const modelName = this.capitalizeFirst(tableName);
    let definition = `model ${modelName} {\n`;
    
    columns.forEach(column => {
      definition += `  ${this.generateFieldDefinition(column)}\n`;
    });
    
    definition += `\n  @@map("${tableName}")\n}`;
    return definition;
  }

  // Tạo field definition cho Prisma schema
  generateFieldDefinition(column) {
    let definition = `${column.name}`;
    
    const typeMap = {
      'int': 'Int',
      'bigint': 'BigInt',
      'smallint': 'Int',
      'tinyint': 'Int',
      'varchar': 'String',
      'char': 'String',
      'text': 'String',
      'longtext': 'String',
      'mediumtext': 'String',
      'tinytext': 'String',
      'datetime': 'DateTime',
      'timestamp': 'DateTime',
      'date': 'DateTime',
      'time': 'DateTime',
      'year': 'Int',
      'boolean': 'Boolean',
      'decimal': 'Decimal',
      'float': 'Float',
      'double': 'Float',
      'json': 'Json',
      'blob': 'Bytes',
      'longblob': 'Bytes',
      'mediumblob': 'Bytes',
      'tinyblob': 'Bytes',
      'enum': 'String',
      'set': 'String'
    };
    
    definition += ` ${typeMap[column.type] || 'String'}`;
    
    if (column.is_primary) {
      definition += ' @id';
    }
    
    if (column.is_auto_increment) {
      definition += ' @default(autoincrement())';
    }
    
    if (column.is_unique) {
      definition += ' @unique';
    }
    
    if (column.default_value && !column.is_auto_increment) {
      if (column.type === 'datetime' && column.default_value === 'CURRENT_TIMESTAMP') {
        definition += ' @default(now())';
      } else if (column.type === 'boolean') {
        definition += ` @default(${column.default_value})`;
      } else if (column.type === 'int' || column.type === 'bigint' || column.type === 'smallint' || column.type === 'tinyint') {
        definition += ` @default(${column.default_value})`;
      } else {
        definition += ` @default("${column.default_value}")`;
      }
    }
    
    if (column.is_nullable) {
      definition += '?';
    }
    
    return definition;
  }

  // Capitalize first letter
  capitalizeFirst(str) {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  // Đảm bảo migration lock file tồn tại
  async ensureMigrationLock() {
    const lockPath = path.join(__dirname, '../prisma/migration_lock.toml');
    try {
      await fs.access(lockPath);
    } catch {
      // Tạo lock file nếu chưa có
      const lockContent = `# Please do not edit this file manually.
# It should be added in your version-control system (i.e. Git).
provider = "mysql"
`;
      await fs.writeFile(lockPath, lockContent);
    }
  }

  // Lấy danh sách migrations
  async getMigrations() {
    try {
      const migrations = await fs.readdir(this.migrationsDir);
      return migrations.filter(dir => {
        return fs.stat(path.join(this.migrationsDir, dir)).then(stat => stat.isDirectory());
      });
    } catch (error) {
      console.error('Error getting migrations:', error);
      return [];
    }
  }

  // Kiểm tra trạng thái migration
  async getMigrationStatus() {
    try {
      const { stdout } = await execAsync('npx prisma migrate status', {
        cwd: path.join(__dirname, '..')
      });
      return { success: true, status: stdout };
    } catch (error) {
      console.error('Error getting migration status:', error);
      return { success: false, error: error.message };
    }
  }

  // Tạo migration cho bảng đã tồn tại trong database
  async createMigrationForExistingTable(tableName, columns) {
    try {
      console.log(`Creating migration for existing table: ${tableName}`);

      // Kiểm tra xem bảng đã có trong schema chưa
      const schemaContent = await fs.readFile(this.schemaPath, 'utf8');
      const modelName = this.capitalizeFirst(tableName);
      
      if (schemaContent.includes(`model ${modelName}`)) {
        console.log(`   ⚠️  Model ${modelName} already exists in schema`);
        return { success: true, message: 'Model already exists in schema' };
      }

      // Tạo nội dung migration SQL (CREATE TABLE IF NOT EXISTS)
      const migrationSQL = this.generateCreateTableIfNotExistsSQL(tableName, columns);
      
      // Tạo tên migration
      const timestamp = new Date().toISOString().replace(/[-:]/g, '').split('.')[0];
      const migrationName = `sync_existing_${tableName}_${timestamp}`;
      const migrationDir = path.join(this.migrationsDir, migrationName);
      
      // Tạo thư mục migration
      await fs.mkdir(migrationDir, { recursive: true });
      
      // Tạo file migration.sql
      const migrationFilePath = path.join(migrationDir, 'migration.sql');
      await fs.writeFile(migrationFilePath, migrationSQL);
      
      // Cập nhật schema.prisma
      await this.addModelToSchema(tableName, columns);
      
      console.log(`Migration created for existing table: ${migrationName}`);
      return { success: true, migrationName, migrationDir };
    } catch (error) {
      console.error('Error creating migration for existing table:', error);
      throw error;
    }
  }

  // Tạo migration cho nhiều bảng đã tồn tại
  async createMigrationsForExistingTables(tables) {
    try {
      console.log(`Creating migrations for ${tables.length} existing tables...`);
      
      const results = [];
      for (const table of tables) {
        try {
          const result = await this.createMigrationForExistingTable(table.name, table.columns);
          results.push({ table: table.name, ...result });
        } catch (error) {
          console.error(`Error creating migration for table ${table.name}:`, error);
          results.push({ table: table.name, success: false, error: error.message });
        }
      }
      
      return results;
    } catch (error) {
      console.error('Error creating migrations for existing tables:', error);
      throw error;
    }
  }

  // Tạo SQL để tạo bảng nếu chưa tồn tại
  generateCreateTableIfNotExistsSQL(tableName, columns) {
    const columnDefinitions = columns.map(col => {
      let definition = `\`${col.name}\` ${this.mapAdminTypeToMySQL(col.type)}`;
      
      if (col.length && (col.type === 'varchar' || col.type === 'int')) {
        definition += `(${col.length})`;
      }
      
      if (!col.is_nullable) {
        definition += ' NOT NULL';
      }
      
      if (col.is_primary) {
        definition += ' PRIMARY KEY';
      }
      
      if (col.is_auto_increment) {
        definition += ' AUTO_INCREMENT';
      }
      
      if (col.is_unique) {
        definition += ' UNIQUE';
      }
      
      if (col.default_value) {
        definition += ` DEFAULT ${col.default_value}`;
      }
      
      return definition;
    }).join(',\n  ');

    return `CREATE TABLE IF NOT EXISTS \`${tableName}\` (\n  ${columnDefinitions}\n);`;
  }

  // Sync tất cả bảng từ database và tạo migrations
  async syncAllTablesFromDatabase() {
    try {
      console.log('Syncing all tables from database and creating migrations...');
      
      // Lấy tất cả bảng từ database
      const tables = await schemaService.getAllTables();
      const existingModels = await this.getExistingModelsFromSchema();
      
      const tablesToSync = [];
      
      for (const tableName of tables) {
        // Bỏ qua các bảng hệ thống
        if (tableName.startsWith('_prisma') || tableName.startsWith('admin_')) {
          continue;
        }
        
        // Kiểm tra xem model đã tồn tại trong schema chưa
        const modelName = this.capitalizeFirst(tableName);
        if (!existingModels.includes(modelName)) {
          // Lấy cấu trúc bảng
          const structure = await schemaService.getTableStructure(tableName);
          
          // Chuyển đổi sang format admin columns
          const columns = structure.columns.map((col, index) => ({
            name: col.Field,
            display_name: this.formatDisplayName(col.Field),
            type: this.mapMySQLTypeToAdmin(col.Type),
            length: this.extractLength(col.Type),
            is_nullable: col.Null === 'YES',
            is_primary: col.Key === 'PRI',
            is_unique: col.Key === 'UNI',
            default_value: col.Default,
            is_auto_increment: col.Extra.includes('auto_increment'),
            order_index: index
          }));
          
          tablesToSync.push({ name: tableName, columns });
        }
      }
      
      if (tablesToSync.length === 0) {
        console.log('No new tables to sync');
        return { success: true, message: 'No new tables to sync', synced: 0 };
      }
      
      // Tạo migrations cho tất cả bảng cần sync
      const results = await this.createMigrationsForExistingTables(tablesToSync);
      
      const successCount = results.filter(r => r.success).length;
      const failedCount = results.filter(r => !r.success).length;
      
      console.log(`Sync completed: ${successCount} successful, ${failedCount} failed`);
      
      return {
        success: true,
        message: `Synced ${successCount} tables, ${failedCount} failed`,
        results,
        synced: successCount
      };
    } catch (error) {
      console.error('Error syncing all tables from database:', error);
      throw error;
    }
  }

  // Lấy danh sách model đã tồn tại trong schema
  async getExistingModelsFromSchema() {
    try {
      const schemaContent = await fs.readFile(this.schemaPath, 'utf8');
      const lines = schemaContent.split('\n');
      const models = [];
      
      for (const line of lines) {
        const trimmedLine = line.trim();
        if (trimmedLine.startsWith('model ')) {
          // Parse tên model đúng cách
          const modelMatch = trimmedLine.match(/^model\s+(\w+)/);
          if (modelMatch) {
            const modelName = modelMatch[1];
            models.push(modelName);
            console.log(`Found model in schema: ${modelName}`);
          }
        }
      }
      
      console.log('All models found in schema:', models);
      return models;
    } catch (error) {
      console.error('Error getting existing models from schema:', error);
      return [];
    }
  }

  // Format tên hiển thị
  formatDisplayName(name) {
    return name.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  }

  // Map MySQL type sang admin type
  mapMySQLTypeToAdmin(mysqlType) {
    const type = mysqlType.toLowerCase();
    if (type.includes('int')) return 'int';
    if (type.includes('varchar')) return 'varchar';
    if (type.includes('text')) return 'text';
    if (type.includes('datetime')) return 'datetime';
    if (type.includes('timestamp')) return 'timestamp';
    if (type.includes('date')) return 'date';
    if (type.includes('boolean') || type.includes('tinyint(1)')) return 'boolean';
    if (type.includes('decimal')) return 'decimal';
    if (type.includes('float')) return 'float';
    if (type.includes('double')) return 'double';
    if (type.includes('json')) return 'json';
    return 'varchar';
  }

  // Trích xuất độ dài từ MySQL type
  extractLength(mysqlType) {
    const match = mysqlType.match(/\((\d+)\)/);
    return match ? parseInt(match[1]) : null;
  }
}

module.exports = new PrismaMigrationService(); 