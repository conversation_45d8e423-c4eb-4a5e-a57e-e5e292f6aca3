# Test Foreign Key Dropdown Feature

## Tính năng đã implement

Đã thêm tính năng tự động tạo dropdown cho foreign key columns trong CRUD forms:

### 1. Template Changes (table-data.ejs)
- Detect foreign key columns bằng cách check `table.relations`
- Render dropdown thay vì text input cho foreign key columns
- Thêm data attributes để identify foreign table và columns

### 2. API Endpoint (adminController.js)
- Thêm method `getRelationDropdownData` để lấy dữ liệu dropdown
- Route: `GET /admin/tables/:tableId/relations/:columnName/dropdown`
- Trả về data với format `{value, label}` từ foreign table

### 3. JavaScript Logic
- Load dropdown data khi modal được mở
- Populate dropdown với current values khi edit record
- Handle async loading để đảm bảo data được load trước khi set values

## Cách hoạt động

1. **Khi tạo relation**: Ví dụ `role_id` trong bảng `role_user` nối với `id` của bảng `role`
2. **Khi mở Add/Edit form**: System sẽ:
   - Detect column `role_id` có relation
   - Tạo dropdown thay vì text input
   - Load data từ bảng `role` với `id` làm value và `name` làm display text
3. **Khi submit**: Dropdown sẽ submit `role_id` value để lưu vào database

## Test Case

### Bước 1: Tạo bảng role
```sql
CREATE TABLE role (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT
);

INSERT INTO role (name, description) VALUES 
('Admin', 'Administrator role'),
('User', 'Regular user role'),
('Manager', 'Manager role');
```

### Bước 2: Tạo bảng role_user với relation
```sql
CREATE TABLE role_user (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  role_id INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Bước 3: Tạo admin table và relation
1. Vào Admin Tables, tạo bảng `role` và `role_user`
2. Vào Table Structure của `role_user`
3. Thêm relation:
   - Local Column: `role_id`
   - Foreign Table: `role`
   - Foreign Column: `id`
   - Display Column: `name`

### Bước 4: Test CRUD
1. Vào Data Management của `role_user`
2. Click "Add" - sẽ thấy dropdown cho `role_id` với options từ bảng `role`
3. Chọn role và submit
4. Edit record - dropdown sẽ hiển thị giá trị hiện tại

## Expected Result

- Form sẽ hiển thị dropdown thay vì text input cho `role_id`
- Dropdown options sẽ hiển thị tên role (Admin, User, Manager)
- Khi submit, sẽ lưu role_id (1, 2, 3) vào database
- Khi edit, dropdown sẽ hiển thị role hiện tại được chọn
