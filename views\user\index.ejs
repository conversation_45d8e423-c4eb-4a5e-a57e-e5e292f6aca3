<div class="container-lg px-4">
    <div class="card">
        <div class="card-header">User</div>
        <div class="card-body">
            <table id="user-table" class="table table-striped table-bordered" style="width:100%">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Fullname</th>
                        <th>Email</th>
                        <th>Created At</th>
                        <th>Updated At</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
            <button type="button" class="btn btn-primary" data-coreui-toggle="modal" data-coreui-target="#addUserModal">
                Add User
            </button>
        </div>
    </div>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1" aria-labelledby="addUserModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addUserModalLabel">Add New User</h5>
                    <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm">
                        <div class="mb-3">
                            <label for="fullname" class="form-label">Fullname</label>
                            <input type="text" class="form-control" id="fullname" name="fullname" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirm Password</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                        <button type="submit" class="btn btn-primary">Save</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit User Modal -->
    <div class="modal fade" id="editUserModal" tabindex="-1" aria-labelledby="editUserModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editUserModalLabel">Edit User</h5>
                    <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editUserForm">
                        <input type="hidden" id="editUserId" name="id">
                        <div class="mb-3">
                            <label for="editFullname" class="form-label">Fullname</label>
                            <input type="text" class="form-control" id="editFullname" name="fullname" required>
                        </div>
                        <div class="mb-3">
                            <label for="editEmail" class="form-label">Email</label>
                            <input type="email" class="form-control" id="editEmail" name="email" required>
                        </div>
                        <div class="mb-3">
                            <label for="editPassword" class="form-label">Password (leave blank to keep current)</label>
                            <input type="password" class="form-control" id="editPassword" name="password">
                        </div>
                        <button type="submit" class="btn btn-primary">Update</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function () {
            console.log('jQuery loaded, initializing DataTable...');
            var table = $('#user-table').DataTable({
                ajax: {
                    url: '/user/data',
                    type: 'GET',
                    error: function (xhr, error, code) {
                        console.error('DataTables error:', error, code);
                        console.error('Response:', xhr.responseText);
                        alert('Error loading data: ' + error);
                    }
                },
                columns: [
                    { data: 'id' },
                    { data: 'fullname' },
                    { data: 'email' },
                    { data: 'createdAt' },
                    { data: 'updatedAt' },
                    {
                        data: null,
                        render: function(data, type, row) {
                            return '<button class="btn btn-sm btn-primary edit-user" data-id="' + row.id + '">Edit</button> ' +
                                '<button class="btn btn-sm btn-danger delete-user" data-id="' + row.id + '">Delete</button>';
                        },
                        orderable: false
                    }
                ],
                processing: true,
                serverSide: true,
                responsive: true,
                pageLength: 25, // Phân trang mặc định 25 dòng
                lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
                language: {
                    lengthMenu: "Hiển thị _MENU_ dòng mỗi trang",
                    zeroRecords: "Không tìm thấy dữ liệu",
                    info: "Hiển thị _START_ đến _END_ của _TOTAL_ dòng",
                    infoEmpty: "Hiển thị 0 đến 0 của 0 dòng",
                    infoFiltered: "(lọc từ _MAX_ tổng số dòng)",
                    search: "Tìm kiếm:",
                    paginate: {
                        first: "Đầu",
                        last: "Cuối",
                        next: "Tiếp",
                        previous: "Trước"
                    }
                }
            });

            // Handle Add User Form
            $('#addUserForm').submit(function (e) {
                e.preventDefault();
                var password = $('#password').val();
                var confirmPassword = $('#confirm_password').val();

                if (password !== confirmPassword) {
                    alert('Passwords do not match!');
                    return;
                }

                $.ajax({
                    url: '/user/add',
                    type: 'POST',
                    data: $(this).serialize(),
                    success: function (data) {
                        $('#addUserModal').modal('hide');
                        $('#addUserForm')[0].reset();
                        table.ajax.reload();
                        alert('User added successfully');
                    },
                    error: function (err) {
                        console.error(err);
                        alert('Error adding user: ' + (err.responseJSON?.message || 'Unknown error'));
                    }
                });
            });

            // Handle Edit User Button Click
            $(document).on('click', '.edit-user', function() {
                var userId = $(this).data('id');
                var rowData = table.row($(this).closest('tr')).data();

                if (rowData) {
                    // Populate edit form with current data
                    $('#editUserId').val(rowData.id);
                    $('#editFullname').val(rowData.fullname);
                    $('#editEmail').val(rowData.email);
                    $('#editPassword').val(''); // Clear password field

                    $('#editUserModal').modal('show');
                } else {
                    console.error('Could not get row data');
                }
            });

            // Handle Edit User Form Submit
            $('#editUserForm').submit(function (e) {
                e.preventDefault();
                var userId = $('#editUserId').val();
                var formData = $(this).serialize();

                $.ajax({
                    url: '/user/' + userId,
                    type: 'PUT',
                    data: formData,
                    success: function (data) {
                        $('#editUserModal').modal('hide');
                        table.ajax.reload();
                        alert('User updated successfully');
                    },
                    error: function (err) {
                        console.error(err);
                        alert('Error updating user: ' + (err.responseJSON?.message || 'Unknown error'));
                    }
                });
            });

            // Handle Delete User Button Click
            $(document).on('click', '.delete-user', function() {
                var userId = $(this).data('id');
                var rowData = table.row($(this).closest('tr')).data();

                if (rowData && confirm('Are you sure you want to delete user "' + rowData.fullname + '"?')) {
                    $.ajax({
                        url: '/user/' + userId,
                        type: 'DELETE',
                        success: function (data) {
                            table.ajax.reload();
                            alert('User deleted successfully');
                        },
                        error: function (err) {
                            console.error(err);
                            alert('Error deleting user: ' + (err.responseJSON?.message || 'Unknown error'));
                        }
                    });
                } else if (!rowData) {
                    console.error('Could not get row data');
                }
            });
        });
    </script>
</div>
