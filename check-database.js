const db = require('./config/database');
const schemaService = require('./services/schemaService');

async function checkDatabase() {
  try {
    console.log('🔍 Checking current database connection...\n');

    // 1. Kết nối database
    console.log('1. Connecting to database...');
    db.connect('check');
    console.log('✅ Database connected\n');

    // 2. <PERSON><PERSON><PERSON> tra thông tin kết nối
    console.log('2. Database connection info:');
    console.log(`   - Host: ${process.env.DB_HOST || 'localhost'}`);
    console.log(`   - User: ${process.env.DB_USER || 'root'}`);
    console.log(`   - Database: ${process.env.DB_NAME || 'coreui'}`);
    console.log(`   - Port: ${process.env.DB_PORT || '3306'}\n`);

    // 3. Kiểm tra database hiện tại
    console.log('3. Current database:');
    const currentDb = await db.queryOne('SELECT DATABASE() as current_db');
    console.log(`   - Current database: ${currentDb.current_db}\n`);

    // 4. <PERSON><PERSON><PERSON> tra bảng message cụ thể
    console.log('4. Checking message table specifically...');
    try {
      const messageTableExists = await db.queryOne(`
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE() 
        AND table_name = 'message'
      `);
      
      if (messageTableExists.count > 0) {
        console.log('   ✅ Table "message" EXISTS in database');
        
        // Kiểm tra cấu trúc bảng
        const tableStructure = await db.query(`DESCRIBE message`);
        console.log('   📊 Table structure:');
        tableStructure.forEach(col => {
          console.log(`      - ${col.Field}: ${col.Type} ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Key ? `(${col.Key})` : ''}`);
        });
        
        // Kiểm tra số lượng records
        const recordCount = await db.queryOne('SELECT COUNT(*) as count FROM message');
        console.log(`   📊 Records count: ${recordCount.count}`);
        
        // Hiển thị vài records đầu
        if (recordCount.count > 0) {
          const sampleRecords = await db.query('SELECT * FROM message LIMIT 3');
          console.log('   📊 Sample records:');
          sampleRecords.forEach((record, index) => {
            console.log(`      ${index + 1}. ${JSON.stringify(record)}`);
          });
        }
      } else {
        console.log('   ❌ Table "message" DOES NOT EXIST in database');
      }
    } catch (error) {
      console.log('   ❌ Error checking message table:', error.message);
    }

    // 5. Liệt kê tất cả bảng trong database
    console.log('\n5. All tables in current database:');
    const allTables = await db.query(`
      SELECT table_name, table_rows, data_length, create_time 
      FROM information_schema.tables 
      WHERE table_schema = DATABASE() 
      ORDER BY table_name
    `);
    
    allTables.forEach(table => {
      console.log(`   - ${table.table_name} (${table.table_rows || 0} rows, created: ${table.create_time || 'unknown'})`);
    });

    // 6. Kiểm tra xem có database khác có tên tương tự không
    console.log('\n6. Checking for similar databases...');
    const databases = await db.query('SHOW DATABASES');
    console.log('   Available databases:');
    databases.forEach(dbInfo => {
      const dbName = dbInfo.Database || dbInfo.database;
      if (dbName === currentDb.current_db) {
        console.log(`   - ${dbName} ← CURRENT`);
      } else {
        console.log(`   - ${dbName}`);
      }
    });

  } catch (error) {
    console.error('❌ Check failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    try {
      await schemaService.closeConnection();
      console.log('\n🔌 Database connection closed');
    } catch (error) {
      console.error('Error closing database connection:', error);
    }
    process.exit(0);
  }
}

checkDatabase();
