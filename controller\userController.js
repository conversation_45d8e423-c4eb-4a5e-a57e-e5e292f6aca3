const passport = require('passport');
const userService = require('../services/userService');

module.exports = {
  register: async (req, res) => {
    try {
      const { fullname, email, password, confirm_password } = req.body;

      // Validate input
      if (!fullname || !email || !password || !confirm_password) {
        return res.status(400).json({ message: 'Please fill in all fields.' });
      }

      if (password !== confirm_password) {
        return res.status(400).json({ message: 'Passwords do not match.' });
      }

      // Check if email format is valid
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.render('register', { title: 'Register', layout: 'login_layout', messages: { error: 'Invalid email format.' } });
      }

      // Check if email already exists
      const existingUser = await userService.findUserByEmail(email);

      if (existingUser) {
        return res.render('register', { title: 'Register', layout: 'login_layout', messages: { error: 'Email already exists.' } });
      }

      // Create user in database
      const user = await userService.createUser(fullname, email, password);

      // Redirect to login page
      res.redirect('/user/login');
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: 'An error occurred during registration.' });
    }
  },
  login: (req, res, next) => {
    passport.authenticate('local', (err, user, info) => {
      if (err) {
        console.error("Authentication error:", err);
        return res.render('login', {
          title: 'Login',
          layout: 'login_layout',
          messages: { error: 'Đã xảy ra lỗi trong quá trình đăng nhập.' }
        });
      }

      if (!user) {
        console.log("Authentication failed:", info.message);
        let errorMessage = 'Đăng nhập thất bại.';

        // Xử lý các thông báo lỗi cụ thể
        if (info && info.message) {
          switch (info.message) {
            case 'Sai mật khẩu.':
              errorMessage = 'Mật khẩu không đúng. Vui lòng thử lại.';
              break;
            case 'Tài khoản chưa được kích hoạt.':
              errorMessage = 'Tài khoản của bạn chưa được kích hoạt. Vui lòng liên hệ quản trị viên.';
              break;
            case 'Tài khoản không tồn tại.':
              errorMessage = 'Email này chưa được đăng ký. Vui lòng kiểm tra lại hoặc đăng ký tài khoản mới.';
              break;
            default:
              errorMessage = info.message;
          }
        }

        return res.render('login', {
          title: 'Login',
          layout: 'login_layout',
          messages: { error: errorMessage }
        });
      }

      req.logIn(user, (err) => {
        if (err) {
          console.error("Login error:", err);
          return res.render('login', {
            title: 'Login',
            layout: 'login_layout',
            messages: { error: 'Đã xảy ra lỗi trong quá trình đăng nhập.' }
          });
        }
        return res.redirect('/');
      });
    })(req, res, next);
  },
  logout: (req, res) => {
    req.logout(function (err) {
      if (err) { return next(err); }
      res.redirect('/user/login');
    });
  },
  getRegister: function (req, res, next) {
    console.log('getRegister');
    try {
      if (req.user) {
        res.redirect('/');
      }else{
        res.render('register', { title: 'Register', layout: 'login_layout', messages: { error: '' } });
      }
    } catch (error) {
      console.error(error);
    }
  },
  getLogin: function (req, res, next) {
    console.log('getLogin', req.user);
    try {
      if (req.user) {
        res.redirect('/');
      }else{
        res.render('login', { title: 'Login', layout: 'login_layout', messages: { error: '' } });
      }
    } catch (error) {
      console.error(error);
    }
  },
  checkEmail: async (req, res) => {
    try {
      const { email } = req.body;

      const existingUser = await userService.findUserByEmail(email);

      res.json({ exists: !!existingUser });
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: 'An error occurred during email check.' });
    }
  },
  index: async function (req, res, next) {
    const breadcrumb = [
          {name: 'Home', url:'/', active: false},
          {name: 'User', url:'/', active: true},
      ];
      res.render('user/index', { title: 'Quản lý user', breadcrumb: breadcrumb });
  },
  getData: async function (req, res, next) {
    try {
      // Get DataTables parameters
      const draw = parseInt(req.query.draw) || 1;
      const start = parseInt(req.query.start) || 0;
      const length = parseInt(req.query.length) || 25;
      const searchValue = req.query.search?.value || '';

      // Calculate page number
      const page = Math.floor(start / length) + 1;

      // Get paginated data
      const result = await userService.getUsersWithPagination(page, length, searchValue);

      // Format data for DataTables
      const formattedUsers = result.data.map(user => ({
        id: user.id,
        fullname: user.fullname,
        email: user.email,
        createdAt: user.createdAt ? new Date(user.createdAt).toLocaleDateString() : '',
        updatedAt: user.updatedAt ? new Date(user.updatedAt).toLocaleDateString() : ''
      }));
      console.log('result', result);
      // Return DataTables server-side format
      res.json({
        draw: draw,
        recordsTotal: result.total,
        recordsFiltered: result.totalFiltered,
        data: formattedUsers
      });
    } catch (error) {
      console.error('Error fetching user data:', error);
      res.status(500).json({ error: 'Failed to fetch user data' });
    }
  },
  addUser: async (req, res) => {
    try {
      const { fullname, email, password } = req.body;
      const user = await userService.addUser(fullname, email, password);
      res.status(201).json(user);
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: 'An error occurred while adding user.' });
    }
  },
  editUser: async (req, res) => {
    try {
      const { id } = req.params;
      const { fullname, email, password } = req.body;
      const user = await userService.editUser(id, fullname, email, password);
      res.status(200).json(user);
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: 'An error occurred while editing user.' });
    }
  },
  deleteUser: async (req, res) => {
    try {
      const { id } = req.params;
      await userService.deleteUser(id);
      res.status(204).send();
    } catch (error) {
      console.error(error);
      res.status(500).json({ message: 'An error occurred while deleting user.' });
    }
  }
};
