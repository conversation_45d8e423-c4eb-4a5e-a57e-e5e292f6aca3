const db = require('./config/database');
const adminService = require('./services/adminService');
const schemaService = require('./services/schemaService');
const dynamicCrudService = require('./services/dynamicCrudService');

async function testAdminFunctions() {
  try {
    console.log('🚀 Starting admin functions test...\n');

    // 1. Kết nối database
    console.log('1. Connecting to database...');
    db.connect('test');
    console.log('✅ Database connected\n');

    // 2. Test tạo bảng mới
    console.log('2. Testing create table...');
    const testTableData = {
      name: 'test_products',
      display_name: 'Test Products',
      description: 'Test table for products',
      model_name: 'TestProduct',
      is_active: true,
      order_index: 0,
      columns: [
        {
          name: 'id',
          display_name: 'ID',
          type: 'int',
          length: 11,
          is_nullable: false,
          is_primary: true,
          is_unique: false,
          is_auto_increment: true,
          is_visible_list: true,
          is_visible_form: false,
          is_searchable: false,
          is_sortable: true,
          form_type: 'input'
        },
        {
          name: 'name',
          display_name: 'Product Name',
          type: 'varchar',
          length: 255,
          is_nullable: false,
          is_primary: false,
          is_unique: false,
          is_auto_increment: false,
          is_visible_list: true,
          is_visible_form: true,
          is_searchable: true,
          is_sortable: true,
          form_type: 'input'
        },
        {
          name: 'price',
          display_name: 'Price',
          type: 'decimal',
          length: '10,2',
          is_nullable: false,
          is_primary: false,
          is_unique: false,
          is_auto_increment: false,
          is_visible_list: true,
          is_visible_form: true,
          is_searchable: false,
          is_sortable: true,
          form_type: 'input'
        },
        {
          name: 'description',
          display_name: 'Description',
          type: 'text',
          is_nullable: true,
          is_primary: false,
          is_unique: false,
          is_auto_increment: false,
          is_visible_list: false,
          is_visible_form: true,
          is_searchable: true,
          is_sortable: false,
          form_type: 'textarea'
        }
      ]
    };

    const createdTable = await adminService.createAdminTable(testTableData);
    console.log('✅ Table created:', createdTable.name);
    console.log('   - Admin table ID:', createdTable.id);
    console.log('   - Columns count:', createdTable.columns.length);

    // 3. Test thêm dữ liệu vào bảng
    console.log('\n3. Testing create record...');
    const testRecord = {
      name: 'Test Product 1',
      price: 99.99,
      description: 'This is a test product'
    };

    const createdRecord = await dynamicCrudService.createRecord('test_products', testRecord);
    console.log('✅ Record created:', createdRecord);

    // 4. Test lấy dữ liệu từ bảng
    console.log('\n4. Testing get table data...');
    const tableData = await dynamicCrudService.getTableData('test_products', {
      page: 1,
      perPage: 10
    });
    console.log('✅ Table data retrieved:');
    console.log('   - Total records:', tableData.total);
    console.log('   - Records:', tableData.data.length);

    // 5. Test cập nhật record
    console.log('\n5. Testing update record...');
    const updatedRecord = await dynamicCrudService.updateRecord('test_products', createdRecord.id, {
      name: 'Updated Test Product 1',
      price: 149.99
    });
    console.log('✅ Record updated:', updatedRecord);

    // 6. Test sync tables từ database
    console.log('\n6. Testing sync tables from database...');
    const syncResult = await adminService.syncTablesFromDatabase();
    console.log('✅ Tables synced:', syncResult.message);
    console.log('   - Results:', syncResult.results.length);

    // 7. Test xóa record
    console.log('\n7. Testing delete record...');
    const deleteResult = await dynamicCrudService.deleteRecord('test_products', createdRecord.id);
    console.log('✅ Record deleted:', deleteResult);

    // 8. Test xóa bảng
    console.log('\n8. Testing delete table...');
    const deleteTableResult = await adminService.deleteAdminTable(createdTable.id);
    console.log('✅ Table deleted:', deleteTableResult);

    console.log('\n🎉 All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    // Đóng kết nối database
    try {
      await schemaService.closeConnection();
      console.log('\n🔌 Database connection closed');
    } catch (error) {
      console.error('Error closing database connection:', error);
    }
    process.exit(0);
  }
}

// Chạy test
testAdminFunctions();
