<div class="container">
  <div class="row justify-content-center">
    <div class="col-md-6">
      <div class="card mb-4 mx-4">
        <div class="card-body p-4">
          <h1>Register</h1>
          <p class="text-body-secondary">Create your account</p>
          <% if (messages.error) { %>
            <div class="alert alert-danger" role="alert">
              <%= messages.error %>
            </div>
          <% } %>
          <form action="/user/register" method="POST" onsubmit="return validateForm()">
            <div class="input-group mb-3">
              <span class="input-group-text">
                <svg class="icon">
                  <use xlink:href="/icons/sprites/free.svg#cil-user"></use>
                </svg>
              </span>
              <input class="form-control" type="text" placeholder="Username" name="fullname" required>
            </div>
            <div class="input-group mb-3">
              <span class="input-group-text">
                <svg class="icon">
                  <use xlink:href="/icons/sprites/free.svg#cil-envelope-open"></use>
                </svg>
              </span>
              <input class="form-control" type="email" placeholder="Email" name="email" required>
            </div>
            <div class="input-group mb-3">
              <span class="input-group-text">
                <svg class="icon">
                  <use xlink:href="/icons/sprites/free.svg#cil-lock-locked"></use>
                </svg>
              </span>
              <input class="form-control" type="password" placeholder="Password" name="password" required>
            </div>
            <div class="input-group mb-4">
              <span class="input-group-text">
                <svg class="icon">
                  <use xlink:href="/icons/sprites/free.svg#cil-lock-locked"></use>
                </svg>
              </span>
              <input class="form-control" type="password" placeholder="Repeat password" name="confirm_password" required>
            </div>
            <button class="btn btn-block btn-success" type="submit">Create Account</button>
          </form>
        </div>
      </div>
    </div>
  </div>
</div>
<script>
  async function validateForm() {
    const fullname = document.querySelector('input[name="fullname"]').value;
    const email = document.querySelector('input[name="email"]').value;
    const password = document.querySelector('input[name="password"]').value;
    const confirm_password = document.querySelector('input[name="confirm_password"]').value;

    if (!fullname || !email || !password || !confirm_password) {
      alert('Please fill in all fields.');
      return false;
    }

    if (password !== confirm_password) {
      alert("Passwords do not match");
      return false;
    }

    // Check if email format is valid
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      alert('Invalid email format.');
      return false;
    }

    // Check if email already exists
    const response = await fetch('/user/check-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ email: email })
    });

    const data = await response.json();

    if (data.exists) {
      alert('Email already exists.');
      return false;
    }

    return true;
  }
</script>
