const db = require('./config/database');

async function checkLoginData() {
  try {
    console.log('🔍 Checking login data...\n');

    // Kết nối database
    db.connect('check');
    console.log('✅ Database connected\n');

    // Ki<PERSON><PERSON> tra users
    console.log('👥 Users in database:');
    const users = await db.query('SELECT id, name, email, created_at FROM user ORDER BY id');
    if (users.length === 0) {
      console.log('❌ No users found!');
    } else {
      users.forEach(user => {
        console.log(`   ${user.id}: ${user.email} (${user.name || 'No name'})`);
      });
    }

    // Kiểm tra roles
    console.log('\n🎭 Roles in database:');
    const roles = await db.query('SELECT id, name, description FROM role ORDER BY id');
    if (roles.length === 0) {
      console.log('❌ No roles found!');
    } else {
      roles.forEach(role => {
        console.log(`   ${role.id}: ${role.name} (${role.description || 'No description'})`);
      });
    }

    // Kiểm tra role_user relationships
    console.log('\n🔗 Role-User relationships:');
    const roleUsers = await db.query(`
      SELECT ru.user_id, ru.role_id, u.email, r.name as role_name 
      FROM role_user ru 
      LEFT JOIN user u ON ru.user_id = u.id 
      LEFT JOIN role r ON ru.role_id = r.id
      ORDER BY ru.user_id
    `);
    if (roleUsers.length === 0) {
      console.log('❌ No role-user relationships found!');
    } else {
      roleUsers.forEach(ru => {
        console.log(`   User ${ru.user_id} (${ru.email || 'Unknown'}) → Role ${ru.role_id} (${ru.role_name || 'Unknown'})`);
      });
    }

    console.log('\n📊 Summary:');
    console.log(`   - Users: ${users.length}`);
    console.log(`   - Roles: ${roles.length}`);
    console.log(`   - Role-User relationships: ${roleUsers.length}`);

    if (users.length === 0 || roles.length === 0 || roleUsers.length === 0) {
      console.log('\n⚠️ Missing critical data detected!');
      console.log('Run the following SQL commands to fix:');
      console.log('\n-- Create admin role');
      console.log("INSERT INTO role (name, description, created_at, updated_at) VALUES ('admin', 'Administrator', NOW(), NOW());");
      console.log('\n-- Create admin user (password: admin123)');
      console.log("INSERT INTO user (name, email, password, email_verified_at, created_at, updated_at) VALUES ('Admin', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NOW(), NOW(), NOW());");
      console.log('\n-- Link admin user to admin role');
      console.log("INSERT INTO role_user (user_id, role_id, created_at, updated_at) VALUES (1, 1, NOW(), NOW());");
    } else {
      console.log('\n✅ Basic login data looks good!');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    process.exit(0);
  }
}

checkLoginData();
