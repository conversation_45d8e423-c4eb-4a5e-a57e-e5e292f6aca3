-- Fix Login Data After Cascade Delete
-- Run these commands in your MySQL client

-- 1. Check current data
SELECT 'USERS:' as info;
SELECT id, name, email, created_at FROM user ORDER BY id;

SELECT 'ROLES:' as info;
SELECT id, name, description FROM role ORDER BY id;

SELECT 'ROLE-USER RELATIONSHIPS:' as info;
SELECT ru.user_id, ru.role_id, u.email, r.name as role_name 
FROM role_user ru 
LEFT JOIN user u ON ru.user_id = u.id 
LEFT JOIN role r ON ru.role_id = r.id
ORDER BY ru.user_id;

-- 2. Create admin role if not exists
INSERT IGNORE INTO role (id, name, description, created_at, updated_at) 
VALUES (1, 'admin', 'Administrator role', NOW(), NOW());

-- 3. Create user role if not exists
INSERT IGNORE INTO role (id, name, description, created_at, updated_at) 
VALUES (2, 'user', 'Regular user role', NOW(), NOW());

-- 4. Create admin user if not exists
-- Password is 'admin123' hashed with bcrypt
INSERT IGNORE INTO user (id, name, email, password, email_verified_at, created_at, updated_at) 
VALUES (1, 'Admin User', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NOW(), NOW(), NOW());

-- 5. Create test user if not exists
-- Password is 'test123' hashed with bcrypt
INSERT IGNORE INTO user (id, name, email, password, email_verified_at, created_at, updated_at) 
VALUES (2, 'Test User', '<EMAIL>', '$2a$10$TKh8H1.PfQx37YgCzwiKb.KjNyWgaHb9cbcoQgdIVFlYg7B77UdFm', NOW(), NOW(), NOW());

-- 6. Link admin user to admin role
INSERT IGNORE INTO role_user (user_id, role_id, created_at, updated_at) 
VALUES (1, 1, NOW(), NOW());

-- 7. Link test user to user role
INSERT IGNORE INTO role_user (user_id, role_id, created_at, updated_at) 
VALUES (2, 2, NOW(), NOW());

-- 8. Verify the fix
SELECT 'FINAL CHECK:' as info;

SELECT 'Users after fix:' as info;
SELECT id, name, email FROM user ORDER BY id;

SELECT 'Roles after fix:' as info;
SELECT id, name, description FROM role ORDER BY id;

SELECT 'Role-User relationships after fix:' as info;
SELECT ru.user_id, ru.role_id, u.email, r.name as role_name 
FROM role_user ru 
JOIN user u ON ru.user_id = u.id 
JOIN role r ON ru.role_id = r.id
ORDER BY ru.user_id;

-- Login credentials:
-- Email: <EMAIL>, Password: admin123, Role: admin
-- Email: <EMAIL>, Password: test123, Role: user
