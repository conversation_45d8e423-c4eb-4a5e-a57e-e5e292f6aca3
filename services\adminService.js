const db = require('../config/database');
const schemaService = require('./schemaService');
const prismaMigrationService = require('./prismaMigrationService');

class AdminService {
  
  // ==================== ADMIN TABLES MANAGEMENT ====================
  
  // Lấy tất cả bảng admin
  async getAllAdminTables() {
    try {
      const tables = await db.query(`
        SELECT * FROM admintable 
        ORDER BY order_index ASC
      `);
      
      // Lấy columns và relations cho từng table
      for (let table of tables) {
        table.columns = await db.query(`
          SELECT * FROM admincolumn 
          WHERE table_id = ? 
          ORDER BY order_index ASC
        `, [table.id]);
        
        table.relations = await db.query(`
          SELECT ar.*, ft.name as foreign_table_name, ac.name as column_name
          FROM adminrelation ar
          LEFT JOIN admintable ft ON ar.foreign_table_id = ft.id
          LEFT JOIN admincolumn ac ON ar.column_id = ac.id
          WHERE ar.table_id = ?
        `, [table.id]);
      }
      
      return tables;
    } catch (error) {
      console.error('Error getting admin tables:', error);
      throw error;
    }
  }

  // Lấy thông tin bảng admin theo ID
  async getAdminTableById(id) {
    try {
      const table = await db.queryOne(`
        SELECT * FROM admintable WHERE id = ?
      `, [id]);
      
      if (!table) return null;
      
      table.columns = await db.query(`
        SELECT * FROM admincolumn 
        WHERE table_id = ? 
        ORDER BY order_index ASC
      `, [table.id]);
      
      table.relations = await db.query(`
        SELECT ar.*, ft.name as foreign_table_name, ac.name as column_name
        FROM adminrelation ar
        LEFT JOIN admintable ft ON ar.foreign_table_id = ft.id
        LEFT JOIN admincolumn ac ON ar.column_id = ac.id
        WHERE ar.table_id = ?
      `, [table.id]);
      
      return table;
    } catch (error) {
      console.error('Error getting admin table:', error);
      throw error;
    }
  }

  // Lấy thông tin bảng admin theo tên
  async getAdminTableByName(name) {
    try {
      const table = await db.queryOne(`
        SELECT * FROM admintable WHERE name = ?
      `, [name]);
      
      if (!table) return null;
      
      table.columns = await db.query(`
        SELECT * FROM admincolumn 
        WHERE table_id = ? 
        ORDER BY order_index ASC
      `, [table.id]);
      
      table.relations = await db.query(`
        SELECT ar.*, ft.name as foreign_table_name, ac.name as column_name
        FROM adminrelation ar
        LEFT JOIN admintable ft ON ar.foreign_table_id = ft.id
        LEFT JOIN admincolumn ac ON ar.column_id = ac.id
        WHERE ar.table_id = ?
      `, [table.id]);
      
      return table;
    } catch (error) {
      console.error('Error getting admin table by name:', error);
      throw error;
    }
  }

  // Tạo bảng admin mới
  async createAdminTable(data) {
    try {
      const { columns, relations, ...tableData } = data;

      // 1. Tạo bảng thực tế trong database trước
      if (columns && columns.length > 0) {
        await schemaService.createTable(tableData.name, columns);
      }

      // 2. Tạo metadata
      return await this.createAdminTableMetadataOnly(data);
    } catch (error) {
      console.error('Error creating admin table:', error);
      throw error;
    }
  }

  // Tạo chỉ metadata admin table (không tạo bảng thực tế)
  async createAdminTableMetadataOnly(data) {
    try {
      const { columns, relations, ...tableData } = data;

      // 1. Lưu thông tin vào admin_tables
      const result = await db.query(`
        INSERT INTO admintable (name, display_name, description, icon, model_name, is_active, order_index)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        tableData.name,
        tableData.display_name,
        tableData.description || null,
        tableData.icon || null,
        tableData.model_name,
        tableData.is_active !== undefined ? tableData.is_active : true,
        tableData.order_index || 0
      ]);

      const adminTableId = result.insertId;

      // 2. Tạo columns metadata
      if (columns && columns.length > 0) {
        for (let i = 0; i < columns.length; i++) {
          const col = columns[i];
          await db.query(`
            INSERT INTO admincolumn (
              table_id, name, display_name, type, length, is_nullable,
              is_primary, is_unique, default_value, is_auto_increment,
              is_visible_list, is_visible_form, is_searchable, is_sortable,
              form_type, validation_rules, order_index
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            adminTableId,
            col.name,
            col.display_name,
            col.type,
            col.length || null,
            col.is_nullable !== undefined ? col.is_nullable : true,
            col.is_primary !== undefined ? col.is_primary : false,
            col.is_unique !== undefined ? col.is_unique : false,
            col.default_value || null,
            col.is_auto_increment !== undefined ? col.is_auto_increment : false,
            col.is_visible_list !== undefined ? col.is_visible_list : true,
            col.is_visible_form !== undefined ? col.is_visible_form : true,
            col.is_searchable !== undefined ? col.is_searchable : false,
            col.is_sortable !== undefined ? col.is_sortable : true,
            col.form_type || 'input',
            col.validation_rules || null,
            i
          ]);
        }
      }

      // 3. Tạo relations nếu có
      if (relations && relations.length > 0) {
        for (const relation of relations) {
          await this.createAdminRelation({
            ...relation,
            table_id: adminTableId
          });
        }
      }

      // Trả về table đã tạo
      return await this.getAdminTableById(adminTableId);
    } catch (error) {
      console.error('Error creating admin table metadata:', error);
      throw error;
    }
  }

  // Cập nhật bảng admin
  async updateAdminTable(id, data) {
    try {
      const { columns, relations, ...tableData } = data;
      
      await db.query(`
        UPDATE admintable 
        SET name = ?, display_name = ?, description = ?, icon = ?, 
            model_name = ?, is_active = ?, order_index = ?, updated_at = NOW()
        WHERE id = ?
      `, [
        tableData.name,
        tableData.display_name,
        tableData.description || null,
        tableData.icon || null,
        tableData.model_name,
        tableData.is_active !== undefined ? tableData.is_active : true,
        tableData.order_index || 0,
        id
      ]);

      return await this.getAdminTableById(id);
    } catch (error) {
      console.error('Error updating admin table:', error);
      throw error;
    }
  }

  // Xóa bảng admin
  async deleteAdminTable(id) {
    try {
      const adminTable = await this.getAdminTableById(id);
      if (!adminTable) {
        throw new Error('Admin table not found');
      }

      // 1. Xóa bảng thực tế trong database trước
      try {
        await schemaService.dropTable(adminTable.name);
      } catch (error) {
        console.warn('Warning: Could not drop table from database:', error.message);
      }

      // 2. Xóa metadata
      return await this.deleteAdminTableMetadataOnly(id);
    } catch (error) {
      console.error('Error deleting admin table:', error);
      throw error;
    }
  }

  // Xóa chỉ metadata admin table (không xóa bảng thực tế)
  async deleteAdminTableMetadataOnly(id) {
    try {
      const adminTable = await this.getAdminTableById(id);
      if (!adminTable) {
        throw new Error('Admin table not found');
      }

      // 1. Xóa relations
      for (const relation of adminTable.relations) {
        await this.deleteAdminRelationMetadataOnly(relation.id);
      }

      // 2. Xóa columns metadata
      await db.query('DELETE FROM admincolumn WHERE table_id = ?', [id]);

      // 3. Xóa table metadata
      await db.query('DELETE FROM admintable WHERE id = ?', [id]);

      return true;
    } catch (error) {
      console.error('Error deleting admin table metadata:', error);
      throw error;
    }
  }

  // ==================== ADMIN COLUMNS MANAGEMENT ====================

  // Thêm cột mới
  async addAdminColumn(tableId, columnData) {
    try {
      // 1. Lấy thông tin bảng
      const adminTable = await this.getAdminTableById(tableId);
      if (!adminTable) {
        throw new Error('Admin table not found');
      }

      // 2. Thêm cột vào bảng thực tế
      await schemaService.addColumn(adminTable.name, columnData);

      // 3. Thêm cột vào metadata
      const result = await db.query(`
        INSERT INTO admincolumn (
          table_id, name, display_name, type, length, is_nullable,
          is_primary, is_unique, default_value, is_auto_increment,
          is_visible_list, is_visible_form, is_searchable, is_sortable,
          form_type, validation_rules, order_index
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        tableId,
        columnData.name,
        columnData.display_name,
        columnData.type,
        columnData.length || null,
        columnData.is_nullable !== undefined ? columnData.is_nullable : true,
        columnData.is_primary !== undefined ? columnData.is_primary : false,
        columnData.is_unique !== undefined ? columnData.is_unique : false,
        columnData.default_value || null,
        columnData.is_auto_increment !== undefined ? columnData.is_auto_increment : false,
        columnData.is_visible_list !== undefined ? columnData.is_visible_list : true,
        columnData.is_visible_form !== undefined ? columnData.is_visible_form : true,
        columnData.is_searchable !== undefined ? columnData.is_searchable : false,
        columnData.is_sortable !== undefined ? columnData.is_sortable : true,
        columnData.form_type || 'input',
        columnData.validation_rules || null,
        columnData.order_index || 0
      ]);

      return { id: result.insertId, ...columnData };
    } catch (error) {
      console.error('Error adding admin column:', error);
      throw error;
    }
  }

  // Cập nhật cột
  async updateAdminColumn(id, columnData) {
    try {
      // 1. Lấy thông tin cột hiện tại
      const currentColumn = await db.queryOne('SELECT * FROM admincolumn WHERE id = ?', [id]);
      if (!currentColumn) {
        throw new Error('Column not found');
      }

      // 2. Lấy thông tin bảng
      const adminTable = await this.getAdminTableById(currentColumn.table_id);
      if (!adminTable) {
        throw new Error('Admin table not found');
      }

      // 3. Cập nhật cột trong bảng thực tế
      await schemaService.modifyColumn(adminTable.name, columnData);

      // 4. Cập nhật metadata
      await db.query(`
        UPDATE admincolumn
        SET name = ?, display_name = ?, type = ?, length = ?, is_nullable = ?,
            is_primary = ?, is_unique = ?, default_value = ?, is_auto_increment = ?,
            is_visible_list = ?, is_visible_form = ?, is_searchable = ?, is_sortable = ?,
            form_type = ?, validation_rules = ?, order_index = ?, updated_at = NOW()
        WHERE id = ?
      `, [
        columnData.name,
        columnData.display_name,
        columnData.type,
        columnData.length || null,
        columnData.is_nullable !== undefined ? columnData.is_nullable : true,
        columnData.is_primary !== undefined ? columnData.is_primary : false,
        columnData.is_unique !== undefined ? columnData.is_unique : false,
        columnData.default_value || null,
        columnData.is_auto_increment !== undefined ? columnData.is_auto_increment : false,
        columnData.is_visible_list !== undefined ? columnData.is_visible_list : true,
        columnData.is_visible_form !== undefined ? columnData.is_visible_form : true,
        columnData.is_searchable !== undefined ? columnData.is_searchable : false,
        columnData.is_sortable !== undefined ? columnData.is_sortable : true,
        columnData.form_type || 'input',
        columnData.validation_rules || null,
        columnData.order_index || 0,
        id
      ]);

      return await db.queryOne('SELECT * FROM admincolumn WHERE id = ?', [id]);
    } catch (error) {
      console.error('Error updating admin column:', error);
      throw error;
    }
  }

  // Xóa cột
  async deleteAdminColumn(id) {
    try {
      // 1. Lấy thông tin cột
      const column = await db.queryOne('SELECT * FROM admincolumn WHERE id = ?', [id]);
      if (!column) {
        throw new Error('Column not found');
      }

      // 2. Lấy thông tin bảng
      const adminTable = await this.getAdminTableById(column.table_id);
      if (!adminTable) {
        throw new Error('Admin table not found');
      }

      // 3. Xóa cột khỏi bảng thực tế
      await schemaService.dropColumn(adminTable.name, column.name);

      // 4. Xóa metadata
      const result = await db.query('DELETE FROM admincolumn WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('Error deleting admin column:', error);
      throw error;
    }
  }

  // ==================== ADMIN RELATIONS MANAGEMENT ====================

  // Tạo relation mới
  async createAdminRelation(relationData) {
    try {
      // 1. Lấy thông tin bảng và cột
      const table = await this.getAdminTableById(relationData.table_id);
      const foreignTable = await this.getAdminTableById(relationData.foreign_table_id);
      const column = await db.queryOne('SELECT * FROM admincolumn WHERE id = ?', [relationData.column_id]);

      if (!table || !foreignTable || !column) {
        throw new Error('Table, foreign table, or column not found');
      }

      // 2. Tạo foreign key trong database
      const constraintName = `fk_${table.name}_${column.name}_${foreignTable.name}`;
      try {
        await schemaService.addForeignKey(
          table.name,
          column.name,
          foreignTable.name,
          relationData.foreign_column,
          constraintName
        );
      } catch (error) {
        console.warn('Warning: Could not create foreign key in database:', error.message);
      }

      // 3. Lưu metadata
      const result = await db.query(`
        INSERT INTO adminrelation (
          table_id, column_id, foreign_table_id, foreign_column,
          display_column, relation_type, on_delete, on_update
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        relationData.table_id,
        relationData.column_id,
        relationData.foreign_table_id,
        relationData.foreign_column,
        relationData.display_column,
        relationData.relation_type || 'belongsTo',
        relationData.on_delete || 'CASCADE',
        relationData.on_update || 'CASCADE'
      ]);

      return { id: result.insertId, ...relationData };
    } catch (error) {
      console.error('Error creating admin relation:', error);
      throw error;
    }
  }

  // Cập nhật relation
  async updateAdminRelation(id, relationData) {
    try {
      await db.query(`
        UPDATE adminrelation 
        SET table_id = ?, column_id = ?, foreign_table_id = ?, foreign_column = ?,
            display_column = ?, relation_type = ?, on_delete = ?, on_update = ?, updated_at = NOW()
        WHERE id = ?
      `, [
        relationData.table_id,
        relationData.column_id,
        relationData.foreign_table_id,
        relationData.foreign_column,
        relationData.display_column,
        relationData.relation_type || 'belongsTo',
        relationData.on_delete || 'CASCADE',
        relationData.on_update || 'CASCADE',
        id
      ]);

      return await db.queryOne('SELECT * FROM adminrelation WHERE id = ?', [id]);
    } catch (error) {
      console.error('Error updating admin relation:', error);
      throw error;
    }
  }

  // Xóa relation
  async deleteAdminRelation(id) {
    try {
      // 1. Lấy thông tin relation
      const relation = await db.queryOne(`
        SELECT ar.*, t.name as table_name, ft.name as foreign_table_name, c.name as column_name
        FROM adminrelation ar
        LEFT JOIN admintable t ON ar.table_id = t.id
        LEFT JOIN admintable ft ON ar.foreign_table_id = ft.id
        LEFT JOIN admincolumn c ON ar.column_id = c.id
        WHERE ar.id = ?
      `, [id]);

      if (!relation) {
        throw new Error('Relation not found');
      }

      // 2. Xóa foreign key khỏi database
      const constraintName = `fk_${relation.table_name}_${relation.column_name}_${relation.foreign_table_name}`;
      try {
        await schemaService.dropForeignKey(relation.table_name, constraintName);
      } catch (error) {
        console.warn('Warning: Could not drop foreign key from database:', error.message);
      }

      // 3. Xóa metadata
      return await this.deleteAdminRelationMetadataOnly(id);
    } catch (error) {
      console.error('Error deleting admin relation:', error);
      throw error;
    }
  }

  // Xóa chỉ metadata relation (không xóa foreign key thực tế)
  async deleteAdminRelationMetadataOnly(id) {
    try {
      const result = await db.query('DELETE FROM adminrelation WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('Error deleting admin relation metadata:', error);
      throw error;
    }
  }

  // ==================== SYNC FUNCTIONS ====================

  // Đồng bộ bảng từ database
  async syncTablesFromDatabase() {
    try {
      // 1. Lấy danh sách bảng từ database
      const dbTables = await schemaService.getAllTables();
      const dbTableNames = dbTables.map(t => t.name);

      // 2. Lấy danh sách bảng từ admin system
      const adminTables = await this.getAllAdminTables();

      const results = [];
      let addedCount = 0;
      let deletedCount = 0;
      let existsCount = 0;

      // 3. Thêm các bảng mới từ database vào admin system
      for (const table of dbTables) {
        try {
          const existingTable = await this.getAdminTableByName(table.name);

          if (!existingTable) {
            // Tạo bảng admin mới (chỉ metadata, không tạo bảng thực tế vì đã tồn tại)
            const adminTable = await this.createAdminTableMetadataOnly({
              name: table.name,
              display_name: this.formatDisplayName(table.name),
              description: `Auto-synced table: ${table.name}`,
              model_name: this.formatModelName(table.name),
              is_active: true,
              order_index: 0,
              columns: table.columns.map((col, index) => ({
                name: col.name,
                display_name: this.formatDisplayName(col.name),
                type: col.type,
                length: col.length,
                is_nullable: col.is_nullable,
                is_primary: col.is_primary,
                is_unique: col.is_unique,
                default_value: col.default_value,
                is_auto_increment: col.is_auto_increment,
                is_visible_list: col.name !== 'id',
                is_visible_form: col.name !== 'id',
                is_searchable: false,
                is_sortable: true,
                form_type: this.mapMySQLTypeToAdmin(col.type),
                order_index: index
              }))
            });

            results.push({
              table: table.name,
              success: true,
              action: 'created',
              data: adminTable
            });
            addedCount++;
          } else {
            results.push({
              table: table.name,
              success: true,
              action: 'exists',
              data: existingTable
            });
            existsCount++;
          }
        } catch (error) {
          results.push({
            table: table.name,
            success: false,
            action: 'error',
            error: error.message
          });
        }
      }

      // 4. Xóa các bảng thừa khỏi admin system (bảng không còn tồn tại trong database)
      for (const adminTable of adminTables) {
        // Bỏ qua các bảng hệ thống admin (chỉ các bảng metadata của hệ thống)
        const systemTables = ['admintable', 'admincolumn', 'adminrelation', 'admin_menus', '_prisma_migrations'];
        if (systemTables.includes(adminTable.name) || adminTable.name.startsWith('_prisma')) {
          continue;
        }

        if (!dbTableNames.includes(adminTable.name)) {
          try {
            // Xóa bảng khỏi admin system (chỉ metadata, không xóa bảng thực tế)
            await this.deleteAdminTableMetadataOnly(adminTable.id);

            results.push({
              table: adminTable.name,
              success: true,
              action: 'deleted',
              message: 'Removed from admin system (table no longer exists in database)'
            });
            deletedCount++;
          } catch (error) {
            results.push({
              table: adminTable.name,
              success: false,
              action: 'delete_error',
              error: error.message
            });
          }
        }
      }

      return {
        success: true,
        message: `Sync completed. Added: ${addedCount}, Deleted: ${deletedCount}, Exists: ${existsCount}`,
        added: addedCount,
        deleted: deletedCount,
        exists: existsCount,
        results
      };
    } catch (error) {
      console.error('Error syncing tables from database:', error);
      throw error;
    }
  }

  // Đồng bộ bảng từ database và tạo migrations
  async syncTablesFromDatabaseWithMigrations() {
    try {
      console.log('Starting sync with migrations...');
      
      // 1. Sync tables vào admin system (như cũ)
      const syncResult = await this.syncTablesFromDatabase();
      
      // 2. Tạo migrations cho những bảng đã tồn tại trong database
      console.log('Creating migrations for existing tables...');
      const migrationResult = await prismaMigrationService.syncAllTablesFromDatabase();
      
      // 3. Regenerate Prisma client nếu có bảng mới được sync
      if (migrationResult.synced > 0) {
        console.log('Regenerating Prisma client...');
        const { exec } = require('child_process');
        const { promisify } = require('util');
        const execAsync = promisify(exec);
        
        try {
          await execAsync('npx prisma generate', {
            cwd: require('path').join(__dirname, '..')
          });
          console.log('Prisma client regenerated successfully');
        } catch (error) {
          console.warn('Warning: Could not regenerate Prisma client:', error.message);
        }
      }
      
      return {
        success: true,
        sync: syncResult,
        migrations: migrationResult,
        message: `Sync completed. Deleted: ${syncResult.deleted}, Added: ${syncResult.added}, Migrations created: ${migrationResult.synced}`
      };
    } catch (error) {
      console.error('Error syncing tables with migrations:', error);
      throw error;
    }
  }

  // Utility functions
  formatDisplayName(name) {
    return name
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  formatModelName(name) {
    return name
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join('');
  }

  mapMySQLTypeToAdmin(mysqlType) {
    const type = mysqlType.toLowerCase();
    if (type.includes('int')) return 'int';
    if (type.includes('bigint')) return 'bigint';
    if (type.includes('smallint')) return 'smallint';
    if (type.includes('tinyint')) return 'tinyint';
    if (type.includes('varchar')) return 'varchar';
    if (type.includes('char')) return 'char';
    if (type.includes('text')) return 'text';
    if (type.includes('longtext')) return 'longtext';
    if (type.includes('mediumtext')) return 'mediumtext';
    if (type.includes('tinytext')) return 'tinytext';
    if (type.includes('datetime')) return 'datetime';
    if (type.includes('timestamp')) return 'timestamp';
    if (type.includes('date')) return 'date';
    if (type.includes('time')) return 'time';
    if (type.includes('year')) return 'year';
    if (type.includes('boolean') || type.includes('tinyint(1)')) return 'boolean';
    if (type.includes('decimal')) return 'decimal';
    if (type.includes('float')) return 'float';
    if (type.includes('double')) return 'double';
    if (type.includes('json')) return 'json';
    if (type.includes('blob')) return 'blob';
    if (type.includes('longblob')) return 'longblob';
    if (type.includes('mediumblob')) return 'mediumblob';
    if (type.includes('tinyblob')) return 'tinyblob';
    if (type.includes('enum')) return 'enum';
    if (type.includes('set')) return 'set';
    return 'varchar';
  }
}

module.exports = new AdminService();
