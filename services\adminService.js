const db = require('../config/database');
const schemaService = require('./schemaService');
const prismaMigrationService = require('./prismaMigrationService');

class AdminService {
  
  // ==================== ADMIN TABLES MANAGEMENT ====================
  
  // Lấy tất cả bảng admin
  async getAllAdminTables() {
    try {
      const tables = await db.query(`
        SELECT * FROM admintable 
        ORDER BY order_index ASC
      `);
      
      // Lấy columns và relations cho từng table
      for (let table of tables) {
        table.columns = await db.query(`
          SELECT * FROM admincolumn 
          WHERE table_id = ? 
          ORDER BY order_index ASC
        `, [table.id]);
        
        table.relations = await db.query(`
          SELECT ar.*, ft.name as foreign_table_name, ac.name as column_name
          FROM adminrelation ar
          LEFT JOIN admintable ft ON ar.foreign_table_id = ft.id
          LEFT JOIN admincolumn ac ON ar.column_id = ac.id
          WHERE ar.table_id = ?
        `, [table.id]);
      }
      
      return tables;
    } catch (error) {
      console.error('Error getting admin tables:', error);
      throw error;
    }
  }

  // Lấy thông tin bảng admin theo ID
  async getAdminTableById(id) {
    try {
      const table = await db.queryOne(`
        SELECT * FROM admintable WHERE id = ?
      `, [id]);
      
      if (!table) return null;
      
      table.columns = await db.query(`
        SELECT * FROM admincolumn 
        WHERE table_id = ? 
        ORDER BY order_index ASC
      `, [table.id]);
      
      table.relations = await db.query(`
        SELECT ar.*, ft.name as foreign_table_name, ac.name as column_name
        FROM adminrelation ar
        LEFT JOIN admintable ft ON ar.foreign_table_id = ft.id
        LEFT JOIN admincolumn ac ON ar.column_id = ac.id
        WHERE ar.table_id = ?
      `, [table.id]);
      
      return table;
    } catch (error) {
      console.error('Error getting admin table:', error);
      throw error;
    }
  }

  // Lấy thông tin bảng admin theo tên
  async getAdminTableByName(name) {
    try {
      const table = await db.queryOne(`
        SELECT * FROM admintable WHERE name = ?
      `, [name]);
      
      if (!table) return null;
      
      table.columns = await db.query(`
        SELECT * FROM admincolumn 
        WHERE table_id = ? 
        ORDER BY order_index ASC
      `, [table.id]);
      
      table.relations = await db.query(`
        SELECT ar.*, ft.name as foreign_table_name, ac.name as column_name
        FROM adminrelation ar
        LEFT JOIN admintable ft ON ar.foreign_table_id = ft.id
        LEFT JOIN admincolumn ac ON ar.column_id = ac.id
        WHERE ar.table_id = ?
      `, [table.id]);
      
      return table;
    } catch (error) {
      console.error('Error getting admin table by name:', error);
      throw error;
    }
  }

  // Tạo bảng admin mới
  async createAdminTable(data) {
    const connection = await db.beginTransaction();
    try {
      const { columns, relations, ...tableData } = data;
      
      // Lưu thông tin vào admin_tables trước
      const result = await db.query(`
        INSERT INTO admintable (name, display_name, description, icon, model_name, is_active, order_index)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        tableData.name,
        tableData.display_name,
        tableData.description || null,
        tableData.icon || null,
        tableData.model_name,
        tableData.is_active !== undefined ? tableData.is_active : true,
        tableData.order_index || 0
      ]);
      
      const adminTableId = result.insertId;
      
      // Tạo columns nếu có
      if (columns && columns.length > 0) {
        for (let i = 0; i < columns.length; i++) {
          const col = columns[i];
          await db.query(`
            INSERT INTO admincolumn (
              table_id, name, display_name, type, length, is_nullable, 
              is_primary, is_unique, default_value, is_auto_increment,
              is_visible_list, is_visible_form, is_searchable, is_sortable,
              form_type, validation_rules, order_index
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            adminTableId,
            col.name,
            col.display_name,
            col.type,
            col.length || null,
            col.is_nullable !== undefined ? col.is_nullable : true,
            col.is_primary !== undefined ? col.is_primary : false,
            col.is_unique !== undefined ? col.is_unique : false,
            col.default_value || null,
            col.is_auto_increment !== undefined ? col.is_auto_increment : false,
            col.is_visible_list !== undefined ? col.is_visible_list : true,
            col.is_visible_form !== undefined ? col.is_visible_form : true,
            col.is_searchable !== undefined ? col.is_searchable : false,
            col.is_sortable !== undefined ? col.is_sortable : true,
            col.form_type || 'input',
            col.validation_rules || null,
            i
          ]);
        }
      }

      // Tạo relations nếu có
      if (relations && relations.length > 0) {
        for (const relation of relations) {
          await this.createAdminRelation({
            ...relation,
            table_id: adminTableId
          });
        }
      }

      await db.commitTransaction(connection);
      
      // Trả về table đã tạo
      return await this.getAdminTableById(adminTableId);
    } catch (error) {
      await db.rollbackTransaction(connection);
      console.error('Error creating admin table:', error);
      throw error;
    }
  }

  // Cập nhật bảng admin
  async updateAdminTable(id, data) {
    try {
      const { columns, relations, ...tableData } = data;
      
      await db.query(`
        UPDATE admintable 
        SET name = ?, display_name = ?, description = ?, icon = ?, 
            model_name = ?, is_active = ?, order_index = ?, updated_at = NOW()
        WHERE id = ?
      `, [
        tableData.name,
        tableData.display_name,
        tableData.description || null,
        tableData.icon || null,
        tableData.model_name,
        tableData.is_active !== undefined ? tableData.is_active : true,
        tableData.order_index || 0,
        id
      ]);

      return await this.getAdminTableById(id);
    } catch (error) {
      console.error('Error updating admin table:', error);
      throw error;
    }
  }

  // Xóa bảng admin
  async deleteAdminTable(id) {
    const connection = await db.beginTransaction();
    try {
      const adminTable = await this.getAdminTableById(id);
      if (!adminTable) {
        throw new Error('Admin table not found');
      }

      // Xóa relations trước
      for (const relation of adminTable.relations) {
        await this.deleteAdminRelation(relation.id);
      }

      // Xóa columns
      await db.query('DELETE FROM admincolumn WHERE table_id = ?', [id]);

      // Xóa table
      await db.query('DELETE FROM admintable WHERE id = ?', [id]);

      await db.commitTransaction(connection);
      return true;
    } catch (error) {
      await db.rollbackTransaction(connection);
      console.error('Error deleting admin table:', error);
      throw error;
    }
  }

  // ==================== ADMIN COLUMNS MANAGEMENT ====================

  // Thêm cột mới
  async addAdminColumn(tableId, columnData) {
    try {
      const result = await db.query(`
        INSERT INTO admincolumn (
          table_id, name, display_name, type, length, is_nullable, 
          is_primary, is_unique, default_value, is_auto_increment,
          is_visible_list, is_visible_form, is_searchable, is_sortable,
          form_type, validation_rules, order_index
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        tableId,
        columnData.name,
        columnData.display_name,
        columnData.type,
        columnData.length || null,
        columnData.is_nullable !== undefined ? columnData.is_nullable : true,
        columnData.is_primary !== undefined ? columnData.is_primary : false,
        columnData.is_unique !== undefined ? columnData.is_unique : false,
        columnData.default_value || null,
        columnData.is_auto_increment !== undefined ? columnData.is_auto_increment : false,
        columnData.is_visible_list !== undefined ? columnData.is_visible_list : true,
        columnData.is_visible_form !== undefined ? columnData.is_visible_form : true,
        columnData.is_searchable !== undefined ? columnData.is_searchable : false,
        columnData.is_sortable !== undefined ? columnData.is_sortable : true,
        columnData.form_type || 'input',
        columnData.validation_rules || null,
        columnData.order_index || 0
      ]);

      return { id: result.insertId, ...columnData };
    } catch (error) {
      console.error('Error adding admin column:', error);
      throw error;
    }
  }

  // Cập nhật cột
  async updateAdminColumn(id, columnData) {
    try {
      await db.query(`
        UPDATE admincolumn 
        SET name = ?, display_name = ?, type = ?, length = ?, is_nullable = ?,
            is_primary = ?, is_unique = ?, default_value = ?, is_auto_increment = ?,
            is_visible_list = ?, is_visible_form = ?, is_searchable = ?, is_sortable = ?,
            form_type = ?, validation_rules = ?, order_index = ?, updated_at = NOW()
        WHERE id = ?
      `, [
        columnData.name,
        columnData.display_name,
        columnData.type,
        columnData.length || null,
        columnData.is_nullable !== undefined ? columnData.is_nullable : true,
        columnData.is_primary !== undefined ? columnData.is_primary : false,
        columnData.is_unique !== undefined ? columnData.is_unique : false,
        columnData.default_value || null,
        columnData.is_auto_increment !== undefined ? columnData.is_auto_increment : false,
        columnData.is_visible_list !== undefined ? columnData.is_visible_list : true,
        columnData.is_visible_form !== undefined ? columnData.is_visible_form : true,
        columnData.is_searchable !== undefined ? columnData.is_searchable : false,
        columnData.is_sortable !== undefined ? columnData.is_sortable : true,
        columnData.form_type || 'input',
        columnData.validation_rules || null,
        columnData.order_index || 0,
        id
      ]);

      return await db.queryOne('SELECT * FROM admincolumn WHERE id = ?', [id]);
    } catch (error) {
      console.error('Error updating admin column:', error);
      throw error;
    }
  }

  // Xóa cột
  async deleteAdminColumn(id) {
    try {
      const result = await db.query('DELETE FROM admincolumn WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('Error deleting admin column:', error);
      throw error;
    }
  }

  // ==================== ADMIN RELATIONS MANAGEMENT ====================

  // Tạo relation mới
  async createAdminRelation(relationData) {
    try {
      const result = await db.query(`
        INSERT INTO adminrelation (
          table_id, column_id, foreign_table_id, foreign_column, 
          display_column, relation_type, on_delete, on_update
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        relationData.table_id,
        relationData.column_id,
        relationData.foreign_table_id,
        relationData.foreign_column,
        relationData.display_column,
        relationData.relation_type || 'belongsTo',
        relationData.on_delete || 'CASCADE',
        relationData.on_update || 'CASCADE'
      ]);

      return { id: result.insertId, ...relationData };
    } catch (error) {
      console.error('Error creating admin relation:', error);
      throw error;
    }
  }

  // Cập nhật relation
  async updateAdminRelation(id, relationData) {
    try {
      await db.query(`
        UPDATE adminrelation 
        SET table_id = ?, column_id = ?, foreign_table_id = ?, foreign_column = ?,
            display_column = ?, relation_type = ?, on_delete = ?, on_update = ?, updated_at = NOW()
        WHERE id = ?
      `, [
        relationData.table_id,
        relationData.column_id,
        relationData.foreign_table_id,
        relationData.foreign_column,
        relationData.display_column,
        relationData.relation_type || 'belongsTo',
        relationData.on_delete || 'CASCADE',
        relationData.on_update || 'CASCADE',
        id
      ]);

      return await db.queryOne('SELECT * FROM adminrelation WHERE id = ?', [id]);
    } catch (error) {
      console.error('Error updating admin relation:', error);
      throw error;
    }
  }

  // Xóa relation
  async deleteAdminRelation(id) {
    try {
      const result = await db.query('DELETE FROM adminrelation WHERE id = ?', [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('Error deleting admin relation:', error);
      throw error;
    }
  }

  // ==================== SYNC FUNCTIONS ====================

  // Đồng bộ bảng từ database
  async syncTablesFromDatabase() {
    try {
      const tables = await schemaService.getAllTables();
      const results = [];

      for (const table of tables) {
        try {
          const existingTable = await this.getAdminTableByName(table.name);
          
          if (!existingTable) {
            // Tạo bảng admin mới
            const adminTable = await this.createAdminTable({
              name: table.name,
              display_name: this.formatDisplayName(table.name),
              description: `Auto-synced table: ${table.name}`,
              model_name: this.formatModelName(table.name),
              is_active: true,
              order_index: 0,
              columns: table.columns.map((col, index) => ({
                name: col.name,
                display_name: this.formatDisplayName(col.name),
                type: col.type,
                length: col.length,
                is_nullable: col.is_nullable,
                is_primary: col.is_primary,
                is_unique: col.is_unique,
                default_value: col.default_value,
                is_auto_increment: col.is_auto_increment,
                is_visible_list: col.name !== 'id',
                is_visible_form: col.name !== 'id',
                is_searchable: false,
                is_sortable: true,
                form_type: this.mapMySQLTypeToAdmin(col.type),
                order_index: index
              }))
            });
            
            results.push({
              table: table.name,
              success: true,
              action: 'created',
              data: adminTable
            });
          } else {
            results.push({
              table: table.name,
              success: true,
              action: 'exists',
              data: existingTable
            });
          }
        } catch (error) {
          results.push({
            table: table.name,
            success: false,
            error: error.message
          });
        }
      }

      return {
        success: true,
        message: `Synced ${results.filter(r => r.success).length} tables`,
        results
      };
    } catch (error) {
      console.error('Error syncing tables from database:', error);
      throw error;
    }
  }

  // Đồng bộ bảng từ database và tạo migrations
  async syncTablesFromDatabaseWithMigrations() {
    try {
      console.log('Starting sync with migrations...');
      
      // 1. Sync tables vào admin system (như cũ)
      const syncResult = await this.syncTablesFromDatabase();
      
      // 2. Tạo migrations cho những bảng đã tồn tại trong database
      console.log('Creating migrations for existing tables...');
      const migrationResult = await prismaMigrationService.syncAllTablesFromDatabase();
      
      // 3. Regenerate Prisma client nếu có bảng mới được sync
      if (migrationResult.synced > 0) {
        console.log('Regenerating Prisma client...');
        const { exec } = require('child_process');
        const { promisify } = require('util');
        const execAsync = promisify(exec);
        
        try {
          await execAsync('npx prisma generate', {
            cwd: require('path').join(__dirname, '..')
          });
          console.log('Prisma client regenerated successfully');
        } catch (error) {
          console.warn('Warning: Could not regenerate Prisma client:', error.message);
        }
      }
      
      return {
        success: true,
        sync: syncResult,
        migrations: migrationResult,
        message: `Sync completed. Deleted: ${syncResult.deleted}, Added: ${syncResult.added}, Migrations created: ${migrationResult.synced}`
      };
    } catch (error) {
      console.error('Error syncing tables with migrations:', error);
      throw error;
    }
  }

  // Utility functions
  formatDisplayName(name) {
    return name
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  formatModelName(name) {
    return name
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join('');
  }

  mapMySQLTypeToAdmin(mysqlType) {
    const type = mysqlType.toLowerCase();
    if (type.includes('int')) return 'int';
    if (type.includes('bigint')) return 'bigint';
    if (type.includes('smallint')) return 'smallint';
    if (type.includes('tinyint')) return 'tinyint';
    if (type.includes('varchar')) return 'varchar';
    if (type.includes('char')) return 'char';
    if (type.includes('text')) return 'text';
    if (type.includes('longtext')) return 'longtext';
    if (type.includes('mediumtext')) return 'mediumtext';
    if (type.includes('tinytext')) return 'tinytext';
    if (type.includes('datetime')) return 'datetime';
    if (type.includes('timestamp')) return 'timestamp';
    if (type.includes('date')) return 'date';
    if (type.includes('time')) return 'time';
    if (type.includes('year')) return 'year';
    if (type.includes('boolean') || type.includes('tinyint(1)')) return 'boolean';
    if (type.includes('decimal')) return 'decimal';
    if (type.includes('float')) return 'float';
    if (type.includes('double')) return 'double';
    if (type.includes('json')) return 'json';
    if (type.includes('blob')) return 'blob';
    if (type.includes('longblob')) return 'longblob';
    if (type.includes('mediumblob')) return 'mediumblob';
    if (type.includes('tinyblob')) return 'tinyblob';
    if (type.includes('enum')) return 'enum';
    if (type.includes('set')) return 'set';
    return 'varchar';
  }
}

module.exports = new AdminService();
