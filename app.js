var createError = require('http-errors');
var express = require('express');
const expressLayouts = require('express-ejs-layouts');
var path = require('path');
var cookieParser = require('cookie-parser');
var logger = require('morgan');
var session = require("express-session");
const MemoryStore = require('memorystore')(session);
const passport = require('passport');
var favicon = require('serve-favicon');

// Initialize database connection
const db = require('./config/database');

var indexRouter = require('./routes/index');
var usersRouter = require('./routes/users');
var adminRouter = require('./routes/admin');
const { loadMenuData } = require('./middleware/menuMiddleware');

require('./config/passport')(passport);

var app = express();

// Initialize database connection
db.connect(process.env.NODE_ENV || 'development');

// view engine setup
app.set('views', path.join(__dirname, 'views'));
app.set('view engine', 'ejs');

// Sử dụng express-ejs-layouts
app.use(expressLayouts);
app.set('layout', 'layout');

app.use(logger('dev'));
app.use(express.json());
app.use(express.urlencoded({ extended: false }));
app.use(cookieParser());
app.use(express.static(path.join(__dirname, 'public')));
app.use(favicon(path.join(__dirname, 'public', '/images', 'favicon.ico')));

app.use(session({
  cookie: { maxAge: 86400000 },
  store: new MemoryStore({
    checkPeriod: 86400000 // prune expired entries every 24h
  }),
  resave: false,
  secret: 'IqaXyt9DGugwUobMVtMKANLFeqZfIrSF',
  saveUninitialized: false
}));

app.use(passport.initialize());
app.use(passport.session());

// Load menu data for sidebar
app.use(loadMenuData);

app.use('/', indexRouter);
app.use('/user', usersRouter);
app.use('/admin', adminRouter);

// catch 404 and forward to error handler
app.use(function(req, res, next) {
  next(createError(404));
});

// error handler
app.use(function(err, req, res, next) {
  // set locals, only providing error in development
  res.locals.message = err.message;
  res.locals.error = req.app.get('env') === 'development' ? err : {};
  console.log('err', err);
  // render the error page
  res.status(err.status || 500);
  if(err.status == 404){
    res.render('404', {layout: 'login_layout'});
  }else{
    res.render('500', {layout: 'login_layout'});
  }
});

module.exports = app;
