const db = require('./config/database');
const adminService = require('./services/adminService');
const schemaService = require('./services/schemaService');

async function debugSync() {
  try {
    console.log('🔍 Debugging sync tables issue...\n');

    // 1. Kết nối database
    console.log('1. Connecting to database...');
    db.connect('debug');
    console.log('✅ Database connected\n');

    // 2. L<PERSON>y danh sách bảng từ database
    console.log('2. Getting tables from database...');
    const dbTables = await schemaService.getAllTables();
    const dbTableNames = dbTables.map(t => t.name);
    console.log('📊 Database tables:');
    dbTableNames.forEach(name => console.log(`   - ${name}`));
    console.log(`   Total: ${dbTableNames.length} tables\n`);

    // 3. <PERSON><PERSON><PERSON> danh sách bảng từ admin system
    console.log('3. Getting tables from admin system...');
    const adminTables = await adminService.getAllAdminTables();
    console.log('📊 Admin system tables:');
    adminTables.forEach(table => console.log(`   - ${table.name} (ID: ${table.id})`));
    console.log(`   Total: ${adminTables.length} tables\n`);

    // 4. Tìm bảng thừa
    console.log('4. Finding orphaned tables...');
    const systemTables = ['admintable', 'admincolumn', 'adminrelation', 'admin_menus', '_prisma_migrations'];
    const orphanedTables = [];

    for (const adminTable of adminTables) {
      // Bỏ qua các bảng hệ thống
      if (systemTables.includes(adminTable.name) || adminTable.name.startsWith('_prisma')) {
        console.log(`   ⚙️ System table (skipped): ${adminTable.name}`);
        continue;
      }

      if (!dbTableNames.includes(adminTable.name)) {
        orphanedTables.push(adminTable);
        console.log(`   ❌ Orphaned table: ${adminTable.name} (ID: ${adminTable.id})`);
      } else {
        console.log(`   ✅ Valid table: ${adminTable.name}`);
      }
    }

    console.log(`\n📊 Summary:`);
    console.log(`   - Database tables: ${dbTableNames.length}`);
    console.log(`   - Admin tables: ${adminTables.length}`);
    console.log(`   - Orphaned tables: ${orphanedTables.length}`);

    if (orphanedTables.length > 0) {
      console.log('\n5. Orphaned tables details:');
      for (const table of orphanedTables) {
        console.log(`\n   Table: ${table.name} (ID: ${table.id})`);
        console.log(`   - Display name: ${table.display_name}`);
        console.log(`   - Description: ${table.description || 'N/A'}`);
        console.log(`   - Created: ${table.created_at}`);
        console.log(`   - Updated: ${table.updated_at}`);
        
        // Kiểm tra columns
        if (table.columns && table.columns.length > 0) {
          console.log(`   - Columns: ${table.columns.length}`);
          table.columns.forEach(col => {
            console.log(`     * ${col.name} (${col.type})`);
          });
        }
      }

      // 6. Hỏi có muốn xóa không
      console.log('\n6. Would you like to remove these orphaned tables? (This is what sync should do)');
      console.log('   Note: This will only remove metadata from admin system, not actual database tables.');
      
      // Simulate what sync would do
      console.log('\n7. Simulating sync operation...');
      for (const table of orphanedTables) {
        try {
          console.log(`   🗑️ Would remove: ${table.name}`);
          // Uncomment the line below to actually remove
          // await adminService.deleteAdminTableMetadataOnly(table.id);
          // console.log(`   ✅ Removed: ${table.name}`);
        } catch (error) {
          console.log(`   ❌ Error removing ${table.name}: ${error.message}`);
        }
      }
      
      console.log('\n💡 To actually remove these tables, uncomment the deletion line in this script and run again.');
    } else {
      console.log('\n✅ No orphaned tables found. Sync should work correctly.');
    }

    // 7. Test actual sync
    console.log('\n8. Testing actual sync function...');
    const syncResult = await adminService.syncTablesFromDatabase();
    console.log('📊 Sync result:', syncResult.message);
    console.log(`   - Added: ${syncResult.added}`);
    console.log(`   - Deleted: ${syncResult.deleted}`);
    console.log(`   - Exists: ${syncResult.exists}`);

    if (syncResult.results && syncResult.results.length > 0) {
      console.log('\n   Detailed results:');
      syncResult.results.forEach(result => {
        const status = result.success ? '✅' : '❌';
        console.log(`   ${status} ${result.action}: ${result.table}`);
        if (result.error) {
          console.log(`      Error: ${result.error}`);
        }
      });
    }

  } catch (error) {
    console.error('❌ Debug failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    try {
      await schemaService.closeConnection();
      console.log('\n🔌 Database connection closed');
    } catch (error) {
      console.error('Error closing database connection:', error);
    }
    process.exit(0);
  }
}

debugSync();
