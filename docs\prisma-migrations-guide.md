# Hướng dẫn sử dụng Prisma Migrations

## Tổng quan

Hệ thống này đã được tích hợp với Prisma Migrations để quản lý database schema một cách an toàn và có kiểm soát. Khi tạo bảng mới thông qua admin panel, hệ thống sẽ tự động tạo Prisma migration files và chạy chúng.

## Cách hoạt động

### 1. Tạo bảng mới

Khi bạn tạo một bảng mới thông qua admin panel:

1. **Tạo migration file**: Hệ thống sẽ tạo một migration file trong thư mục `prisma/migrations/`
2. **Cập nhật schema.prisma**: Model tương ứng sẽ được thêm vào file `schema.prisma`
3. **Chạy migration**: Migration sẽ được chạy tự động
4. **Regenerate Prisma client**: Prisma client sẽ được tái tạo để bao gồm model mới

### 2. <PERSON><PERSON><PERSON> bảng

Khi bạn xóa một bảng:

1. **Tạo drop migration**: Migration để xóa bảng sẽ được tạo
2. **Cập nhật schema.prisma**: Model sẽ bị xóa khỏi schema
3. **Chạy migration**: Migration xóa bảng sẽ được chạy
4. **Regenerate Prisma client**: Prisma client sẽ được cập nhật

### 3. Sync Tables với Migrations (Mới)

Khi bạn có những bảng đã được tạo trực tiếp trong database (không qua tool):

1. **Sync tables**: Đồng bộ bảng từ database vào admin system
2. **Tạo migrations**: Tự động tạo migration files cho những bảng chưa có trong Prisma schema
3. **Cập nhật schema.prisma**: Thêm models cho những bảng mới
4. **Regenerate Prisma client**: Cập nhật Prisma client

## Sử dụng trong Production

### 1. Sync Tables với Migrations

Khi deploy lên production với database đã có sẵn:

```bash
# 1. Sync tables và tạo migrations
# Sử dụng nút "Sync & Create Migrations" trong admin panel
# Hoặc gọi API: POST /admin/sync-tables-with-migrations

# 2. Deploy migrations
npx prisma migrate deploy

# 3. Regenerate Prisma client
npx prisma generate

# 4. Start application
npm start
```

### 2. Deploy Script với Sync

Tạo script deploy trong `package.json`:

```json
{
  "scripts": {
    "deploy": "npx prisma migrate deploy && npx prisma generate && npm start",
    "deploy-with-sync": "node scripts/sync-and-deploy.js && npm start"
  }
}
```

### 3. Script Sync và Deploy

Tạo file `scripts/sync-and-deploy.js`:

```javascript
const adminService = require('../services/adminService');
const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

async function syncAndDeploy() {
  try {
    console.log('🔄 Syncing tables with migrations...');
    
    // Sync tables và tạo migrations
    const result = await adminService.syncTablesFromDatabaseWithMigrations();
    console.log('✅ Sync completed:', result.message);
    
    // Deploy migrations
    console.log('🚀 Deploying migrations...');
    await execAsync('npx prisma migrate deploy', { cwd: process.cwd() });
    console.log('✅ Migrations deployed');
    
    // Generate Prisma client
    console.log('🔧 Generating Prisma client...');
    await execAsync('npx prisma generate', { cwd: process.cwd() });
    console.log('✅ Prisma client generated');
    
  } catch (error) {
    console.error('❌ Deploy failed:', error.message);
    process.exit(1);
  }
}

syncAndDeploy();
```

### 4. CI/CD Pipeline với Sync

```yaml
# Ví dụ với GitHub Actions
- name: Deploy to production
  run: |
    # Sync tables và tạo migrations
    curl -X POST http://localhost:3000/admin/sync-tables-with-migrations
    
    # Deploy migrations
    npx prisma migrate deploy
    
    # Generate client
    npx prisma generate
    
    # Start application
    npm start
```

## Quản lý Migrations

### 1. Xem trạng thái migrations

```bash
npx prisma migrate status
```

### 2. Reset migrations (chỉ trong development)

```bash
npx prisma migrate reset
```

### 3. Tạo migration thủ công

```bash
npx prisma migrate dev --name add_new_column
```

## Các loại Migration

### 1. Create Table Migration
```sql
CREATE TABLE `users` (
  `id` INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(255) NOT NULL,
  `email` VARCHAR(255) NOT NULL UNIQUE,
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

### 2. Drop Table Migration
```sql
DROP TABLE IF EXISTS `old_table`;
```

### 3. Sync Existing Table Migration
```sql
CREATE TABLE IF NOT EXISTS `existing_table` (
  `id` INT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `name` VARCHAR(255) NOT NULL,
  `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
```

## Lưu ý quan trọng

### 1. Backup Database

Luôn backup database trước khi chạy migrations trong production:

```bash
mysqldump -u username -p database_name > backup.sql
```

### 2. Test Migrations

Luôn test migrations trong môi trường staging trước khi deploy production:

```bash
# Test migration
npx prisma migrate dev
```

### 3. Sync Strategy

Khi sync tables với migrations:

1. **Development**: Sync thường xuyên để cập nhật schema
2. **Staging**: Sync trước khi test
3. **Production**: Sync một lần khi setup, sau đó chỉ dùng migrations

### 4. Migration Lock

File `migration_lock.toml` đảm bảo chỉ một provider được sử dụng:

```toml
provider = "mysql"
```

## Troubleshooting

### 1. Migration Failed

Nếu migration thất bại:

```bash
# Xem log chi tiết
npx prisma migrate status

# Reset nếu cần (chỉ development)
npx prisma migrate reset
```

### 2. Schema Drift

Nếu có sự khác biệt giữa schema và database:

```bash
# Sync tables và tạo migrations
curl -X POST http://localhost:3000/admin/sync-tables-with-migrations

# Hoặc tạo migration để sync
npx prisma migrate dev --name fix_schema_drift
```

### 3. Prisma Client Outdated

Nếu Prisma client không có model mới:

```bash
npx prisma generate
```

### 4. Sync Conflicts

Nếu có xung đột khi sync:

1. **Backup database**
2. **Review conflicts** trong admin panel
3. **Manual fix** nếu cần
4. **Re-sync** sau khi fix

## Best Practices

### 1. Migration Naming

Đặt tên migration rõ ràng:

```
create_users_table
sync_existing_products_table
add_email_to_users
drop_old_columns
```

### 2. Atomic Changes

Mỗi migration chỉ nên thực hiện một thay đổi logic:

```sql
-- ✅ Tốt: Một thay đổi
ALTER TABLE users ADD COLUMN email VARCHAR(255);

-- ❌ Không tốt: Nhiều thay đổi không liên quan
ALTER TABLE users ADD COLUMN email VARCHAR(255);
ALTER TABLE products ADD COLUMN price DECIMAL;
```

### 3. Data Migration

Nếu cần migrate data:

```sql
-- Tạo cột mới
ALTER TABLE users ADD COLUMN full_name VARCHAR(255);

-- Migrate data
UPDATE users SET full_name = CONCAT(first_name, ' ', last_name);

-- Xóa cột cũ
ALTER TABLE users DROP COLUMN first_name, DROP COLUMN last_name;
```

### 4. Testing

Luôn test migrations:

1. **Unit test**: Test logic migration
2. **Integration test**: Test với database thật
3. **Rollback test**: Test khả năng rollback
4. **Sync test**: Test sync với migrations

## Kết luận

Prisma Migrations cung cấp cách quản lý database schema an toàn và có kiểm soát. Hệ thống này đã được tích hợp để tự động tạo và chạy migrations khi tạo/xóa bảng thông qua admin panel.

**Đặc biệt quan trọng**: Chức năng "Sync & Create Migrations" cho phép bạn đồng bộ những bảng đã tồn tại trong database và tạo migrations cho chúng, giúp deploy an toàn lên production.

Đảm bảo luôn backup database và test migrations trước khi deploy production. 