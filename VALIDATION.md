# Validation System Documentation

## Overview

Hệ thống validation toàn diện cho admin tool, bao gồm validation ở cả frontend và backend để đảm bảo dữ liệu hợp lệ và tránh lỗi SQL.

## Backend Validation

### File: `utils/validation.js`

#### Classes:
- `ValidationError`: Custom error class cho validation errors
- `Validator`: Main validation class với static methods

#### Methods:

##### Table Validation:
- `validateTableName(name)`: Validate tên bảng
- `validateDisplayName(name)`: Validate display name
- `validateTableData(data)`: Validate toàn bộ dữ liệu bảng

##### Column Validation:
- `validateColumnName(name)`: Validate tên cột
- `validateColumnType(type)`: Validate kiểu dữ liệu cột
- `validateColumnLength(type, length)`: Validate độ dài cột
- `validateDefaultValue(type, value, isNullable)`: Validate giá trị mặc định
- `validateColumnData(column, index)`: Validate toàn bộ dữ liệu cột

##### Record Validation:
- `validateRecordData(data, columns)`: Validate dữ liệu record
- `validateFieldValue(value, column)`: Validate giá trị field cụ thể

### Rules:

#### Table Names:
- ✅ Chỉ chứa chữ cái, số, gạch dưới
- ✅ Bắt đầu bằng chữ cái hoặc gạch dưới
- ✅ Tối đa 64 ký tự
- ✅ Không được là từ khóa MySQL reserved
- ❌ Không được chứa dấu cách, ký tự đặc biệt

#### Column Names:
- ✅ Chỉ chứa chữ cái, số, gạch dưới
- ✅ Bắt đầu bằng chữ cái hoặc gạch dưới
- ✅ Tối đa 64 ký tự
- ✅ Không được là từ khóa MySQL reserved
- ✅ Không được trùng lặp trong cùng bảng
- ❌ Không được chứa dấu cách, ký tự đặc biệt

#### Column Types:
- ✅ Các kiểu hợp lệ: int, bigint, smallint, tinyint, mediumint, varchar, char, text, mediumtext, longtext, decimal, float, double, date, datetime, timestamp, time, year, boolean, tinyint(1), json, blob, mediumblob, longblob

#### Column Length:
- **Numeric types**: 1-255
- **String types**: 1-65535
- **Decimal**: Format "precision,scale" (e.g., "10,2")

#### Primary Key:
- ✅ Ít nhất một cột phải là primary key
- ✅ Primary key không được nullable
- ✅ Auto increment chỉ cho integer primary key

#### Record Data:
- ✅ Required fields không được empty
- ✅ Kiểu dữ liệu phải đúng
- ✅ Độ dài không vượt quá giới hạn
- ✅ Format date hợp lệ
- ✅ JSON hợp lệ cho JSON fields

## Frontend Validation

### File: `views/admin/tables.ejs`

#### Functions:
- `validateTableName(name)`: Validate tên bảng
- `validateColumnName(name)`: Validate tên cột
- `validateDisplayName(name)`: Validate display name
- `validateColumnType(type)`: Validate kiểu cột
- `validateColumnLength(type, length)`: Validate độ dài cột

#### Real-time Validation:
- Input validation khi user typing
- Visual feedback với Bootstrap classes (`is-invalid`)
- Error messages hiển thị ngay lập tức

### File: `public/js/table-data-validation.js`

#### Class: `TableDataValidator`

#### Methods:
- `validateField(value, column)`: Validate field cụ thể
- `validateForm(formData, columns)`: Validate toàn bộ form
- `displayErrors(errors)`: Hiển thị errors
- `clearErrors()`: Xóa errors
- `setupRealTimeValidation(columns)`: Setup real-time validation
- `sanitizeInput(value)`: Sanitize input để tránh XSS
- `formatValue(value, column)`: Format value để hiển thị

## Controller Integration

### File: `controller/adminController.js`

#### Updated Methods:
- `createTable`: Sử dụng `Validator.validateTableData()`
- `addColumn`: Sử dụng `Validator.validateColumnData()`
- `updateColumn`: Sử dụng `Validator.validateColumnData()`
- `createRecord`: Sử dụng `Validator.validateRecordData()`
- `updateRecord`: Sử dụng `Validator.validateRecordData()`

#### Error Handling:
```javascript
if (error instanceof ValidationError) {
  return res.status(400).json({ 
    success: false, 
    message: error.message,
    errors: error.errors || [{ field: error.field, message: error.message }]
  });
}
```

## Security Features

### SQL Injection Prevention:
- ✅ Validate input format
- ✅ Reject dangerous characters
- ✅ Use parameterized queries
- ✅ Sanitize user input

### XSS Prevention:
- ✅ HTML encode output
- ✅ Sanitize input values
- ✅ Validate JSON content

### Reserved Keywords Protection:
- ✅ Block MySQL reserved words
- ✅ Prevent system table conflicts

## Usage Examples

### Backend Validation:
```javascript
try {
  const validatedData = Validator.validateTableData(req.body);
  const table = await adminService.createAdminTable(validatedData);
  res.json({ success: true, data: table });
} catch (error) {
  if (error instanceof ValidationError) {
    return res.status(400).json({ 
      success: false, 
      message: error.message,
      errors: error.errors 
    });
  }
  res.status(500).json({ success: false, message: error.message });
}
```

### Frontend Validation:
```javascript
// Real-time validation
$(document).on('input', '[name="name"]', function() {
  const value = $(this).val();
  const error = validateTableName(value);
  
  if (error) {
    $(this).addClass('is-invalid');
    $(this).after(`<div class="invalid-feedback">${error}</div>`);
  } else {
    $(this).removeClass('is-invalid');
    $(this).siblings('.invalid-feedback').remove();
  }
});

// Form validation
const errors = [];
const tableName = document.querySelector('[name="name"]').value;
const tableNameError = validateTableName(tableName);
if (tableNameError) {
  errors.push(`Table name: ${tableNameError}`);
}

if (errors.length > 0) {
  alert('Validation errors:\n\n' + errors.join('\n'));
  return;
}
```

## Error Messages

### Vietnamese Error Messages:
- "Tên bảng không được để trống"
- "Tên cột chỉ được chứa chữ cái, số và dấu gạch dưới"
- "Ít nhất một cột phải là primary key"
- "Kiểu dữ liệu không hợp lệ"

### English Error Messages:
- "Table name is required"
- "Column name can only contain letters, numbers, and underscores"
- "At least one column must be marked as primary key"
- "Invalid column type"

## Testing

### Test Coverage:
- ✅ Valid table data
- ✅ Invalid table names
- ✅ Reserved keywords
- ✅ Invalid column types
- ✅ Duplicate column names
- ✅ Missing primary key
- ✅ Record validation
- ✅ Field value validation

### Run Tests:
```bash
node test-validation.js
```

## Benefits

1. **Data Integrity**: Đảm bảo dữ liệu hợp lệ trước khi lưu vào database
2. **Security**: Ngăn chặn SQL injection và XSS attacks
3. **User Experience**: Feedback ngay lập tức khi user nhập liệu
4. **Error Prevention**: Tránh lỗi runtime và database errors
5. **Consistency**: Validation rules nhất quán giữa frontend và backend
6. **Maintainability**: Code dễ maintain và extend

## Future Enhancements

- [ ] Custom validation rules per table
- [ ] Async validation for unique constraints
- [ ] Bulk validation for import operations
- [ ] Validation rule builder UI
- [ ] Integration with database constraints
- [ ] Performance optimization for large datasets
