const db = require('../config/database');

class DynamicCrudService {
  constructor() {
    // Sử dụng database connection pool
  }

  // Lấy dữ liệu với phân trang và tìm kiếm
  async getTableData(tableName, options = {}) {
    try {
      const {
        page = 1,
        perPage = 25,
        search = '',
        orderBy = 'id',
        orderDirection = 'desc',
        filters = {}
      } = options;

      // Lấy thông tin bảng admin
      const adminTable = await db.queryOne(`
        SELECT * FROM admintable WHERE name = ?
      `, [tableName]);

      if (!adminTable) {
        throw new Error('Table not found in admin configuration');
      }

      // Lấy columns
      const columns = await db.query(`
        SELECT * FROM admincolumn 
        WHERE table_id = ? AND is_visible_list = true
        ORDER BY order_index ASC
      `, [adminTable.id]);

      // Lấy relations
      const relations = await db.query(`
        SELECT ar.*, ft.name as foreign_table_name, ac.name as column_name
        FROM adminrelation ar
        LEFT JOIN admintable ft ON ar.foreign_table_id = ft.id
        LEFT JOIN admincolumn ac ON ar.column_id = ac.id
        WHERE ar.table_id = ?
      `, [adminTable.id]);

      adminTable.columns = columns;
      adminTable.relations = relations;

      // Xây dựng điều kiện WHERE
      let whereConditions = [];
      let queryParams = [];

      // Thêm điều kiện tìm kiếm
      if (search) {
        const searchableColumns = columns.filter(col => col.is_searchable);
        if (searchableColumns.length > 0) {
          const searchConditions = searchableColumns.map(col => `\`${col.name}\` LIKE ?`);
          whereConditions.push(`(${searchConditions.join(' OR ')})`);
          searchableColumns.forEach(() => queryParams.push(`%${search}%`));
        }
      }

      // Thêm filters
      Object.keys(filters).forEach(key => {
        if (filters[key] !== null && filters[key] !== undefined && filters[key] !== '') {
          whereConditions.push(`\`${key}\` = ?`);
          queryParams.push(filters[key]);
        }
      });

      // Xây dựng câu query
      let selectColumns = columns.map(col => `\`${col.name}\``).join(', ');
      let whereClause = whereConditions.length > 0 ? ` WHERE ${whereConditions.join(' AND ')}` : '';
      let orderClause = '';

      // Thêm ORDER BY
      if (orderBy && columns.find(col => col.name === orderBy && col.is_sortable)) {
        orderClause = ` ORDER BY \`${orderBy}\` ${orderDirection.toUpperCase()}`;
      }

      // Thêm LIMIT và OFFSET
      const offset = (page - 1) * perPage;
      const limitClause = ` LIMIT ${perPage} OFFSET ${offset}`;

      // Thực hiện queries
      const dataQuery = `SELECT ${selectColumns} FROM \`${tableName}\`${whereClause}${orderClause}${limitClause}`;
      const countQuery = `SELECT COUNT(*) as total FROM \`${tableName}\`${whereClause}`;

      const [rows, countResult] = await Promise.all([
        db.query(dataQuery, queryParams),
        db.queryOne(countQuery, queryParams)
      ]);

      const total = Number(countResult.total);

      // Xử lý dữ liệu với relations
      const processedRows = await this.processRowsWithRelations(rows, adminTable);

      return {
        data: processedRows,
        total,
        totalFiltered: total,
        page,
        perPage,
        totalPages: Math.ceil(total / perPage)
      };
    } catch (error) {
      console.error('Error getting table data:', error);
      throw error;
    }
  }

  // Xử lý dữ liệu với relations
  async processRowsWithRelations(rows, adminTable) {
    for (const row of rows) {
      for (const relation of adminTable.relations) {
        const foreignKeyValue = row[relation.column_name];
        if (foreignKeyValue) {
          try {
            const query = `SELECT \`${relation.display_column}\` FROM \`${relation.foreign_table_name}\` WHERE \`${relation.foreign_column}\` = ?`;
            const foreignRows = await db.query(query, [foreignKeyValue]);

            if (foreignRows.length > 0) {
              row[`${relation.column_name}_display`] = foreignRows[0][relation.display_column];
            }
          } catch (error) {
            console.error('Error fetching relation data:', error);
          }
        }
      }
    }

    return rows;
  }

  // Lấy một record theo ID
  async getRecord(tableName, id) {
    try {
      const rows = await db.query(`SELECT * FROM \`${tableName}\` WHERE id = ?`, [id]);
      return rows[0] || null;
    } catch (error) {
      console.error('Error getting record:', error);
      throw error;
    }
  }

  // Tạo record mới
  async createRecord(tableName, data) {
    try {
      // Lấy thông tin bảng admin
      const adminTable = await db.queryOne(`
        SELECT * FROM admintable WHERE name = ?
      `, [tableName]);

      if (!adminTable) {
        throw new Error('Table not found in admin configuration');
      }

      // Lấy columns
      const columns = await db.query(`
        SELECT * FROM admincolumn 
        WHERE table_id = ? AND is_visible_form = true
      `, [adminTable.id]);

      // Lọc dữ liệu theo cấu hình columns
      const filteredData = {};
      columns.forEach(col => {
        if (data.hasOwnProperty(col.name) && col.name !== 'id') {
          filteredData[col.name] = data[col.name];
        }
      });

      // Xây dựng câu INSERT
      const columnNames = Object.keys(filteredData);
      const values = Object.values(filteredData);
      const placeholders = columnNames.map(() => '?').join(', ');

      const query = `INSERT INTO \`${tableName}\` (\`${columnNames.join('`, `')}\`) VALUES (${placeholders})`;

      const result = await db.query(query, values);

      // Trả về record vừa tạo
      return await this.getRecord(tableName, result.insertId);
    } catch (error) {
      console.error('Error creating record:', error);
      throw error;
    }
  }

  // Cập nhật record
  async updateRecord(tableName, id, data) {
    try {
      // Lấy thông tin bảng admin
      const adminTable = await db.queryOne(`
        SELECT * FROM admintable WHERE name = ?
      `, [tableName]);

      if (!adminTable) {
        throw new Error('Table not found in admin configuration');
      }

      // Lấy columns
      const columns = await db.query(`
        SELECT * FROM admincolumn 
        WHERE table_id = ? AND is_visible_form = true
      `, [adminTable.id]);

      // Lọc dữ liệu theo cấu hình columns
      const filteredData = {};
      columns.forEach(col => {
        if (data.hasOwnProperty(col.name) && col.name !== 'id') {
          filteredData[col.name] = data[col.name];
        }
      });

      // Xây dựng câu UPDATE
      const setClause = Object.keys(filteredData).map(key => `\`${key}\` = ?`).join(', ');
      const values = Object.values(filteredData);
      values.push(id);

      const query = `UPDATE \`${tableName}\` SET ${setClause} WHERE id = ?`;

      const result = await db.query(query, values);

      if (result.affectedRows === 0) {
        throw new Error('Record not found');
      }

      // Trả về record đã cập nhật
      return await this.getRecord(tableName, id);
    } catch (error) {
      console.error('Error updating record:', error);
      throw error;
    }
  }

  // Xóa record
  async deleteRecord(tableName, id) {
    try {
      const result = await db.query(`DELETE FROM \`${tableName}\` WHERE id = ?`, [id]);
      return result.affectedRows > 0;
    } catch (error) {
      console.error('Error deleting record:', error);
      throw error;
    }
  }

  // Lấy dữ liệu cho dropdown
  async getDropdownData(tableName, valueColumn = 'id', displayColumn = 'name') {
    try {
      const query = `SELECT \`${valueColumn}\`, \`${displayColumn}\` FROM \`${tableName}\` ORDER BY \`${displayColumn}\` ASC`;
      return await db.query(query);
    } catch (error) {
      console.error('Error getting dropdown data:', error);
      throw error;
    }
  }

  // Validate dữ liệu
  validateData(data, columns) {
    const errors = [];

    columns.forEach(column => {
      const value = data[column.name];

      // Kiểm tra required
      if (!column.is_nullable && (value === null || value === undefined || value === '')) {
        errors.push(`${column.display_name} is required`);
        return;
      }

      // Kiểm tra type
      if (value !== null && value !== undefined && value !== '') {
        switch (column.type.toLowerCase()) {
          case 'int':
          case 'bigint':
          case 'smallint':
          case 'tinyint':
            if (isNaN(Number(value))) {
              errors.push(`${column.display_name} must be a number`);
            }
            break;
          case 'decimal':
          case 'float':
          case 'double':
            if (isNaN(Number(value))) {
              errors.push(`${column.display_name} must be a valid number`);
            }
            break;
          case 'datetime':
          case 'timestamp':
            if (isNaN(Date.parse(value))) {
              errors.push(`${column.display_name} must be a valid date`);
            }
            break;
          case 'boolean':
            if (!['true', 'false', '1', '0', true, false, 1, 0].includes(value)) {
              errors.push(`${column.display_name} must be true or false`);
            }
            break;
        }
      }

      // Kiểm tra length cho varchar
      if (column.type.toLowerCase().includes('varchar') && column.length) {
        const maxLength = parseInt(column.length);
        if (value && value.toString().length > maxLength) {
          errors.push(`${column.display_name} must not exceed ${maxLength} characters`);
        }
      }
    });

    return errors;
  }
}

module.exports = new DynamicCrudService();
