const adminService = require('../services/adminService');
const schemaService = require('../services/schemaService');
const dynamicCrudService = require('../services/dynamicCrudService');

module.exports = {
  // ==================== DASHBOARD ====================
  
  // Trang chủ admin
  index: async (req, res) => {
    try {
      const breadcrumb = [
        { name: 'Home', url: '/', active: false },
        { name: 'Admin', url: '/admin', active: true }
      ];

      const tables = await adminService.getAllAdminTables();
      const totalTables = tables.length;
      const activeTables = tables.filter(t => t.is_active).length;

      res.render('admin/index', {
        title: 'Database Admin',
        breadcrumb,
        stats: {
          totalTables,
          activeTables,
          totalColumns: tables.reduce((sum, t) => sum + t.columns.length, 0),
          totalRelations: tables.reduce((sum, t) => sum + t.relations.length, 0)
        },
        tables
      });
    } catch (error) {
      console.error('Error in admin index:', error);
      res.status(500).render('500', { layout: 'login_layout' });
    }
  },

  // ==================== TABLES MANAGEMENT ====================

  // Danh sách bảng
  tables: async (req, res) => {
    try {
      const breadcrumb = [
        { name: 'Home', url: '/', active: false },
        { name: 'Admin', url: '/admin', active: false },
        { name: 'Tables', url: '/admin/tables', active: true }
      ];

      res.render('admin/tables', {
        title: 'Quản lý bảng',
        breadcrumb
      });
    } catch (error) {
      console.error('Error in tables page:', error);
      res.status(500).render('500', { layout: 'login_layout' });
    }
  },

  // API lấy dữ liệu bảng
  getTablesData: async (req, res) => {
    try {
      const draw = parseInt(req.query.draw) || 1;
      const start = parseInt(req.query.start) || 0;
      const length = parseInt(req.query.length) || 25;
      const searchValue = req.query.search?.value || '';

      const tables = await adminService.getAllAdminTables();
      
      // Filter theo search
      let filteredTables = tables;
      if (searchValue) {
        filteredTables = tables.filter(table => 
          table.name.toLowerCase().includes(searchValue.toLowerCase()) ||
          table.display_name.toLowerCase().includes(searchValue.toLowerCase())
        );
      }

      // Pagination
      const total = tables.length;
      const totalFiltered = filteredTables.length;
      const paginatedTables = filteredTables.slice(start, start + length);

      // Format data
      const formattedTables = paginatedTables.map(table => ({
        id: table.id,
        name: table.name,
        display_name: table.display_name,
        description: table.description || '',
        columns_count: table.columns.length,
        relations_count: table.relations.length,
        is_active: table.is_active,
        created_at: new Date(table.created_at).toLocaleDateString(),
        actions: `
          <button class="btn btn-sm btn-info view-table" data-id="${table.id}" title="Xem cấu trúc">
            <i class="fas fa-eye"></i>
          </button>
          <button class="btn btn-sm btn-primary edit-table" data-id="${table.id}" title="Chỉnh sửa">
            <i class="fas fa-edit"></i>
          </button>
          <button class="btn btn-sm btn-success manage-data" data-id="${table.id}" title="Quản lý dữ liệu">
            <i class="fas fa-database"></i>
          </button>
          <button class="btn btn-sm btn-danger delete-table" data-id="${table.id}" title="Xóa">
            <i class="fas fa-trash"></i>
          </button>
        `
      }));

      res.json({
        draw,
        recordsTotal: total,
        recordsFiltered: totalFiltered,
        data: formattedTables
      });
    } catch (error) {
      console.error('Error getting tables data:', error);
      res.status(500).json({ error: 'Failed to fetch tables data' });
    }
  },

  // Tạo bảng mới
  createTable: async (req, res) => {
    try {
      const tableData = req.body;
      const table = await adminService.createAdminTable(tableData);
      res.status(201).json({ success: true, data: table });
    } catch (error) {
      console.error('Error creating table:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Lấy thông tin bảng
  getTable: async (req, res) => {
    try {
      const { id } = req.params;
      const table = await adminService.getAdminTableById(parseInt(id));

      if (!table) {
        return res.status(404).json({ success: false, message: 'Table not found' });
      }

      res.json({ success: true, data: table });
    } catch (error) {
      console.error('Error getting table:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Cập nhật bảng
  updateTable: async (req, res) => {
    try {
      const { id } = req.params;
      const tableData = req.body;
      const table = await adminService.updateAdminTable(parseInt(id), tableData);
      res.json({ success: true, data: table });
    } catch (error) {
      console.error('Error updating table:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Xóa bảng
  deleteTable: async (req, res) => {
    try {
      const { id } = req.params;
      await adminService.deleteAdminTable(parseInt(id));
      res.json({ success: true, message: 'Table deleted successfully' });
    } catch (error) {
      console.error('Error deleting table:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // ==================== COLUMNS MANAGEMENT ====================

  // Thêm cột
  addColumn: async (req, res) => {
    try {
      const { tableId } = req.params;
      const columnData = req.body;
      const column = await adminService.addAdminColumn(parseInt(tableId), columnData);
      res.status(201).json({ success: true, data: column });
    } catch (error) {
      console.error('Error adding column:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Cập nhật cột
  updateColumn: async (req, res) => {
    try {
      const { id } = req.params;
      const columnData = req.body;
      const column = await adminService.updateAdminColumn(parseInt(id), columnData);
      res.json({ success: true, data: column });
    } catch (error) {
      console.error('Error updating column:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Xóa cột
  deleteColumn: async (req, res) => {
    try {
      const { id } = req.params;
      await adminService.deleteAdminColumn(parseInt(id));
      res.json({ success: true, message: 'Column deleted successfully' });
    } catch (error) {
      console.error('Error deleting column:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // ==================== RELATIONS MANAGEMENT ====================

  // Tạo relation
  createRelation: async (req, res) => {
    try {
      const relationData = req.body;
      const relation = await adminService.createAdminRelation(relationData);
      res.status(201).json({ success: true, data: relation });
    } catch (error) {
      console.error('Error creating relation:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Xóa relation
  deleteRelation: async (req, res) => {
    try {
      const { id } = req.params;
      await adminService.deleteAdminRelation(parseInt(id));
      res.json({ success: true, message: 'Relation deleted successfully' });
    } catch (error) {
      console.error('Error deleting relation:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // ==================== SYNC FUNCTIONS ====================

  // Đồng bộ bảng từ database
  syncTables: async (req, res) => {
    try {
      const result = await adminService.syncTablesFromDatabase();
      res.json({ success: true, ...result });
    } catch (error) {
      console.error('Error syncing tables:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Đồng bộ bảng từ database và tạo migrations
  syncTablesWithMigrations: async (req, res) => {
    try {
      const result = await adminService.syncTablesFromDatabaseWithMigrations();
      res.json({ success: true, ...result });
    } catch (error) {
      console.error('Error syncing tables with migrations:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },
  // Trang sửa cấu trúc bảng
  tableStructure: async (req, res) => {
    try {
      const { tableId } = req.params;
      const adminTable = await adminService.getAdminTableById(parseInt(tableId));

      if (!adminTable) {
        return res.status(404).render('404', { layout: 'login_layout' });
      }

      // Lấy cấu trúc thực tế từ database
      const dbStructure = await schemaService.getTableStructure(adminTable.name);

      // Lấy danh sách tất cả bảng để làm foreign key reference
      const allTables = await adminService.getAllAdminTables();

      const breadcrumb = [
        { name: 'Home', url: '/', active: false },
        { name: 'Admin', url: '/admin', active: false },
        { name: 'Tables', url: '/admin/tables', active: false },
        { name: adminTable.display_name, url: `/admin/tables/${tableId}/structure`, active: true }
      ];

      res.render('admin/table-structure', {
        title: `Table Structure - ${adminTable.display_name}`,
        breadcrumb,
        table: adminTable,
        dbStructure,
        allTables
      });
    } catch (error) {
      console.error('Error getting table structure page:', error);
      res.status(500).render('500', { layout: 'login_layout' });
    }
  },

  // Cập nhật cấu trúc bảng (thêm/sửa/xóa cột)
  updateTableStructure: async (req, res) => {
    try {
      const { tableId } = req.params;
      const { action, columnData } = req.body;

      const adminTable = await adminService.getAdminTableById(parseInt(tableId));
      if (!adminTable) {
        return res.status(404).json({ success: false, message: 'Table not found' });
      }

      let result;
      switch (action) {
        case 'add_column':
          result = await adminService.addAdminColumn(parseInt(tableId), columnData);
          break;
        case 'modify_column':
          result = await adminService.updateAdminColumn(parseInt(columnData.id), columnData);
          break;
        case 'drop_column':
          result = await adminService.deleteAdminColumn(parseInt(columnData.id));
          break;
        default:
          return res.status(400).json({ success: false, message: 'Invalid action' });
      }

      res.json({ success: true, data: result, message: 'Table structure updated successfully' });
    } catch (error) {
      console.error('Error updating table structure:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // ==================== TABLE DATA MANAGEMENT ====================

  // Trang quản lý dữ liệu bảng
  tableData: async (req, res) => {
    try {
      const { tableId } = req.params;
      const table = await adminService.getAdminTableById(parseInt(tableId));

      if (!table) {
        return res.status(404).render('404', { layout: 'login_layout' });
      }

      const breadcrumb = [
        { name: 'Home', url: '/', active: false },
        { name: 'Admin', url: '/admin', active: false },
        { name: 'Tables', url: '/admin/tables', active: false },
        { name: table.display_name, url: `/admin/tables/${tableId}/data`, active: true }
      ];

      res.render('admin/table-data', {
        title: `Quản lý dữ liệu - ${table.display_name}`,
        breadcrumb,
        table
      });
    } catch (error) {
      console.error('Error in table data page:', error);
      res.status(500).render('500', { layout: 'login_layout' });
    }
  },
  // API lấy dữ liệu bảng
  getTableDataApi: async (req, res) => {
    try {
      const { tableId } = req.params;
      const adminTable = await adminService.getAdminTableById(parseInt(tableId));

      if (!adminTable) {
        return res.status(404).json({ success: false, message: 'Table not found' });
      }

      const draw = parseInt(req.query.draw) || 1;
      const start = parseInt(req.query.start) || 0;
      const length = parseInt(req.query.length) || 25;
      const searchValue = req.query.search?.value || '';
      const orderColumnIndex = parseInt(req.query.order?.[0]?.column) || 0;
      const orderDirection = req.query.order?.[0]?.dir || 'desc';

      // Lấy tên cột để sort
      const visibleColumns = adminTable.columns.filter(col => col.is_visible_list);
      const orderColumn = visibleColumns[orderColumnIndex]?.name || 'id';

      const page = Math.floor(start / length) + 1;

      const result = await dynamicCrudService.getTableData(adminTable.name, {
        page,
        perPage: length,
        search: searchValue,
        orderBy: orderColumn,
        orderDirection
      });

      // Format data cho DataTables
      const formattedData = result.data.map(row => {
        const formattedRow = {};

        // Thêm dữ liệu các cột visible
        visibleColumns.forEach(col => {
          if (row[`${col.name}_display`]) {
            formattedRow[col.name] = row[`${col.name}_display`];
          } else {
            formattedRow[col.name] = row[col.name];
          }
        });

        // Thêm actions
        formattedRow.actions = `
          <button class="btn btn-sm btn-primary edit-record" data-id="${row.id}" title="Edit">
            <i class="fas fa-edit"></i>
          </button>
          <button class="btn btn-sm btn-danger delete-record" data-id="${row.id}" title="Delete">
            <i class="fas fa-trash"></i>
          </button>
        `;

        return formattedRow;
      });

      res.json({
        draw,
        recordsTotal: result.total,
        recordsFiltered: result.totalFiltered,
        data: formattedData
      });
    } catch (error) {
      console.error('Error getting table data API:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },
  // API lấy dữ liệu của bảng
  getTableData: async (req, res) => {
    try {
      const { tableId } = req.params;
      const table = await adminService.getAdminTableById(parseInt(tableId));

      if (!table) {
        return res.status(404).json({ success: false, message: 'Table not found' });
      }

      const options = {
        page: parseInt(req.query.page) || 1,
        perPage: parseInt(req.query.perPage) || 25,
        search: req.query.search || '',
        orderBy: req.query.orderBy || 'id',
        orderDirection: req.query.orderDirection || 'desc',
        filters: req.query.filters ? JSON.parse(req.query.filters) : {}
      };

      const result = await dynamicCrudService.getTableData(table.name, options);
      res.json({ success: true, ...result });
    } catch (error) {
      console.error('Error getting table data:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },
  
  // Lấy một record
  getRecord: async (req, res) => {
    try {
      const { tableId, recordId } = req.params;
      const adminTable = await adminService.getAdminTableById(parseInt(tableId));

      if (!adminTable) {
        return res.status(404).json({ success: false, message: 'Table not found' });
      }

      const record = await dynamicCrudService.getRecord(adminTable.name, recordId);

      if (!record) {
        return res.status(404).json({ success: false, message: 'Record not found' });
      }

      res.json({ success: true, data: record });
    } catch (error) {
      console.error('Error getting record:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // API tạo record mới
  createRecord: async (req, res) => {
    try {
      const { tableId } = req.params;
      const table = await adminService.getAdminTableById(parseInt(tableId));

      if (!table) {
        return res.status(404).json({ success: false, message: 'Table not found' });
      }

       // Validate dữ liệu
       const formColumns = table.columns.filter(col => col.is_visible_form);
       const errors = dynamicCrudService.validateData(req.body, formColumns);

       if (errors.length > 0) {
         return res.status(400).json({ success: false, message: 'Validation failed', errors });
       }

      const record = await dynamicCrudService.createRecord(table.name, req.body);
      res.status(201).json({ success: true, data: record });
    } catch (error) {
      console.error('Error creating record:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // API cập nhật record
  updateRecord: async (req, res) => {
    try {
      const { tableId, recordId } = req.params;
      const table = await adminService.getAdminTableById(parseInt(tableId));

      if (!table) {
        return res.status(404).json({ success: false, message: 'Table not found' });
      }

        // Validate dữ liệu
        const formColumns = table.columns.filter(col => col.is_visible_form);
        const errors = dynamicCrudService.validateData(req.body, formColumns);
  
        if (errors.length > 0) {
          return res.status(400).json({ success: false, message: 'Validation failed', errors });
        }
      const record = await dynamicCrudService.updateRecord(table.name, parseInt(recordId), req.body);
      res.json({ success: true, data: record });
    } catch (error) {
      console.error('Error updating record:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // API xóa record
  deleteRecord: async (req, res) => {
    try {
      const { tableId, recordId } = req.params;
      const table = await adminService.getAdminTableById(parseInt(tableId));

      if (!table) {
        return res.status(404).json({ success: false, message: 'Table not found' });
      }

      await dynamicCrudService.deleteRecord(table.name, parseInt(recordId));
      res.json({ success: true, message: 'Record deleted successfully' });
    } catch (error) {
      console.error('Error deleting record:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // API lấy dữ liệu dropdown
  getDropdownData: async (req, res) => {
    try {
      const { tableName } = req.params;
      const { valueColumn, displayColumn } = req.query;
      
      const data = await dynamicCrudService.getDropdownData(
        tableName, 
        valueColumn || 'id', 
        displayColumn || 'name'
      );
      
      res.json({ success: true, data });
    } catch (error) {
      console.error('Error getting dropdown data:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },
  // Lấy dữ liệu dropdown cho relation cụ thể
  getRelationDropdownData: async (req, res) => {
    try {
      const { tableId, columnName } = req.params;

      // Lấy thông tin bảng admin
      const adminTable = await adminService.getAdminTableById(parseInt(tableId));
      if (!adminTable) {
        return res.status(404).json({ success: false, message: 'Table not found' });
      }

      // Tìm relation cho column này
      const relation = adminTable.relations.find(rel => rel.column.name === columnName);
      if (!relation) {
        return res.status(404).json({ success: false, message: 'Relation not found for this column' });
      }

      // Lấy dữ liệu dropdown từ foreign table
      const data = await dynamicCrudService.getDropdownData(
        relation.foreign_table.name,
        relation.foreign_column,
        relation.display_column
      );

      res.json({ success: true, data });
    } catch (error) {
      console.error('Error getting relation dropdown data:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },
};
