# Cascade Delete System Documentation

## Overview

Hệ thống cascade delete tự động xử lý việc xóa records có foreign key constraints bằng cách xóa tất cả related records trước khi xóa parent record.

## Problem Solved

**Before**: 
```
Error deleting record: Cannot delete or update a parent row: 
a foreign key constraint fails (`coreui`.`role_user`, 
CONSTRAINT `role_user_user_id_fkey` FOREIGN KEY (`user_id`) 
REFERENCES `user` (`id`) ON UPDATE CASCADE)
```

**After**: 
```
✅ Cascade delete completed: 5 records deleted in 234ms
```

## Features

### 🔍 **Foreign Key Detection**
- Tự động phát hiện foreign key constraints
- <PERSON><PERSON><PERSON> danh sách tất cả bảng tham chiếu
- Đ<PERSON>m số lượng related records

### 🔥 **Cascade Delete**
- Xóa tất cả child records trước
- Xóa parent record cuối cùng
- Recursive delete cho nested relationships
- Transaction-safe operations

### ⚠️ **Safety Features**
- Hi<PERSON><PERSON> thị thông tin chi tiết trước khi xóa
- <PERSON><PERSON><PERSON> c<PERSON>u x<PERSON><PERSON> "DELETE" cho cascade operations
- Logging chi tiết cho audit trail
- Error handling và rollback

### 📊 **User Experience**
- Real-time related records checking
- Detailed impact preview
- Progress tracking
- Clear error messages

## Implementation

### Backend Components

#### 1. **DynamicCrudService** (`services/dynamicCrudService.js`)

##### Methods:
```javascript
// Main delete method with cascade detection
async deleteRecord(tableName, id)

// Cascade delete implementation
async deleteRecordWithCascade(tableName, id)

// Get foreign key references
async getForeignKeyReferences(tableName)

// Check related records before delete
async getRelatedRecords(tableName, id)
```

##### Flow:
1. Try direct delete first
2. If foreign key error → trigger cascade delete
3. Find all foreign key references
4. Delete child records recursively
5. Delete parent record
6. Log operations

#### 2. **AdminController** (`controller/adminController.js`)

##### New Endpoints:
```javascript
// Check related records before delete
GET /admin/tables/:tableId/records/:recordId/related

// Delete record (with cascade if needed)
DELETE /admin/tables/:tableId/records/:recordId
```

##### Error Handling:
- `ER_ROW_IS_REFERENCED_2`: Foreign key constraint
- `ER_NO_REFERENCED_ROW_2`: Foreign key violation
- Detailed error messages

### Frontend Components

#### 1. **Enhanced Delete Confirmation** (`views/admin/table-data.ejs`)

##### Features:
- Pre-delete related records check
- Detailed impact preview
- Cascade warning with sample data
- "DELETE" confirmation for dangerous operations

##### Flow:
1. User clicks delete button
2. Check related records via API
3. Show appropriate confirmation dialog
4. Execute delete operation
5. Refresh table data

## Usage Examples

### Simple Delete (No Foreign Keys)
```javascript
// User clicks delete on message record
// → Standard confirmation dialog
// → Direct delete
// → Success message
```

### Cascade Delete (With Foreign Keys)
```javascript
// User clicks delete on user record
// → Check related records
// → Show cascade warning:
//   "⚠️ CASCADE DELETE WARNING ⚠️
//    This record has related data:
//    📋 role_user: 3 record(s)
//    📋 user_sessions: 12 record(s)
//    🔥 TOTAL IMPACT: 16 records will be deleted"
// → Require "DELETE" confirmation
// → Execute cascade delete
// → Success with count
```

## Database Schema Detection

### Foreign Key Query:
```sql
SELECT 
  TABLE_NAME as table_name,
  COLUMN_NAME as column_name,
  CONSTRAINT_NAME as constraint_name,
  REFERENCED_TABLE_NAME as referenced_table_name,
  REFERENCED_COLUMN_NAME as referenced_column_name
FROM 
  information_schema.KEY_COLUMN_USAGE 
WHERE 
  REFERENCED_TABLE_SCHEMA = DATABASE()
  AND REFERENCED_TABLE_NAME = ?
  AND REFERENCED_COLUMN_NAME = 'id'
```

### Related Records Count:
```sql
SELECT COUNT(*) as count 
FROM `child_table` 
WHERE `foreign_key_column` = ?
```

## Logging & Monitoring

### Console Logs:
```
🔥 Starting cascade delete for user:1
📋 Found 2 foreign key references for user
🔍 Found 3 child records in role_user
  ➤ Deleting role_user:1
  ➤ Deleting role_user:2
  ➤ Deleting role_user:3
🔍 Found 12 child records in user_sessions
  ➤ Deleting user_sessions:1
  ➤ Deleting user_sessions:2
  ...
🎯 Deleting parent record user:1
✅ Cascade delete completed: 16 records deleted in 234ms
```

### Error Logs:
```
❌ Cascade delete failed after 156ms: Error message
```

## Security Considerations

### 1. **Authorization**
- Ensure user has delete permissions
- Check table-level access controls
- Audit log all delete operations

### 2. **Data Integrity**
- Validate foreign key relationships
- Prevent orphaned records
- Maintain referential integrity

### 3. **Performance**
- Limit cascade depth
- Batch operations where possible
- Monitor large delete operations

## Configuration

### Environment Variables:
```env
# Database connection (already configured)
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=
DB_NAME=coreui
```

### Settings:
- Max cascade depth: Unlimited (recursive)
- Batch size: Individual records
- Timeout: Default MySQL timeout
- Logging: Console (production should use proper logger)

## API Reference

### Check Related Records
```http
GET /admin/tables/:tableId/records/:recordId/related

Response:
{
  "success": true,
  "hasRelatedRecords": true,
  "relatedRecords": [
    {
      "table": "role_user",
      "column": "user_id", 
      "count": 3,
      "samples": [{"id": 1, "user_id": 1, "role_id": 2}]
    }
  ]
}
```

### Delete Record
```http
DELETE /admin/tables/:tableId/records/:recordId

Response (Success):
{
  "success": true,
  "message": "Record deleted successfully (including related records if any)"
}

Response (Error):
{
  "success": false,
  "message": "Cannot delete record: it is referenced by other records"
}
```

## Testing

### Test Scenarios:
1. ✅ Delete record without foreign keys
2. ✅ Delete record with foreign keys (cascade)
3. ✅ Delete record with nested foreign keys
4. ✅ Handle foreign key constraint errors
5. ✅ Check related records count
6. ✅ Display cascade warning
7. ✅ Require confirmation for dangerous deletes

### Test Commands:
```bash
# Run cascade delete tests
node test-cascade-delete.js

# Test specific scenarios
# (Tests are integrated into the main application)
```

## Benefits

### 1. **User Experience**
- ✅ No more cryptic foreign key errors
- ✅ Clear understanding of delete impact
- ✅ Safe delete operations
- ✅ Detailed feedback

### 2. **Data Integrity**
- ✅ Maintains referential integrity
- ✅ Prevents orphaned records
- ✅ Consistent database state
- ✅ Audit trail

### 3. **Developer Experience**
- ✅ Automatic foreign key handling
- ✅ No manual cascade logic needed
- ✅ Comprehensive logging
- ✅ Error handling built-in

### 4. **System Reliability**
- ✅ Graceful error handling
- ✅ Transaction safety
- ✅ Performance monitoring
- ✅ Rollback capabilities

## Future Enhancements

- [ ] Soft delete option
- [ ] Bulk cascade delete
- [ ] Delete preview mode
- [ ] Custom cascade rules
- [ ] Performance optimization for large datasets
- [ ] Database-level cascade configuration
- [ ] Undo delete functionality
- [ ] Export deleted data before cascade

## Troubleshooting

### Common Issues:

1. **Foreign key not detected**
   - Check database schema
   - Verify foreign key constraints exist
   - Check table names and column names

2. **Cascade delete too slow**
   - Monitor large datasets
   - Consider batch operations
   - Optimize foreign key queries

3. **Permission errors**
   - Verify DELETE permissions
   - Check foreign table access
   - Validate user authorization

### Debug Commands:
```sql
-- Check foreign keys for a table
SELECT * FROM information_schema.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_NAME = 'your_table';

-- Check related records count
SELECT COUNT(*) FROM child_table WHERE foreign_key = 'value';
```
