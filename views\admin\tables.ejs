<div class="container-lg px-4">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Database Tables Management</h5>
            <div>
                <button type="button" class="btn btn-success me-2" onclick="syncTables()">
                    <i class="fas fa-sync me-1"></i>Sync Tables
                </button>
                <button type="button" class="btn btn-warning me-2" onclick="syncTablesWithMigrations()">
                    <i class="fas fa-database me-1"></i>Sync & Create Migrations
                </button>
                <button type="button" class="btn btn-primary" data-coreui-toggle="modal" data-coreui-target="#createTableModal">
                    <i class="fas fa-plus me-1"></i>Create Table
                </button>
            </div>
        </div>
        <div class="card-body">
            <table id="tables-table" class="table table-striped table-bordered" style="width:100%">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Table Name</th>
                        <th>Display Name</th>
                        <th>Description</th>
                        <th>Columns</th>
                        <th>Relations</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Create Table Modal -->
<div class="modal fade" id="createTableModal" tabindex="-1" aria-labelledby="createTableModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createTableModalLabel">Create New Table</h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="createTableForm">
                    <!-- Basic Info -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tableName" class="form-label">Table Name *</label>
                                <input type="text" class="form-control" id="tableName" name="name" required>
                                <div class="form-text">Use lowercase with underscores (e.g., user_profiles)</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="displayName" class="form-label">Display Name *</label>
                                <input type="text" class="form-control" id="displayName" name="display_name" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="modelName" class="form-label">Model Name *</label>
                                <input type="text" class="form-control" id="modelName" name="model_name" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="icon" class="form-label">Icon</label>
                                <input type="text" class="form-control" id="icon" name="icon" placeholder="fas fa-table">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="orderIndex" class="form-label">Order Index</label>
                                <input type="number" class="form-control" id="orderIndex" name="order_index" value="0">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-check mb-4">
                        <input class="form-check-input" type="checkbox" id="isActive" name="is_active" checked>
                        <label class="form-check-label" for="isActive">
                            Active (show in admin panel)
                        </label>
                    </div>

                    <!-- Columns Section -->
                    <h6>Table Columns</h6>
                    <div id="columnsContainer">
                        <div class="column-row border rounded p-3 mb-3">
                            <div class="row">
                                <div class="col-md-3">
                                    <label class="form-label">Column Name *</label>
                                    <input type="text" class="form-control column-name" name="columns[0][name]" value="id" required>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Type *</label>
                                    <select class="form-select column-type" name="columns[0][type]" required>
                                        <option value="int" selected>INT</option>
                                        <option value="bigint">BIGINT</option>
                                        <option value="smallint">SMALLINT</option>
                                        <option value="tinyint">TINYINT</option>
                                        <option value="varchar">VARCHAR</option>
                                        <option value="char">CHAR</option>
                                        <option value="text">TEXT</option>
                                        <option value="longtext">LONGTEXT</option>
                                        <option value="mediumtext">MEDIUMTEXT</option>
                                        <option value="tinytext">TINYTEXT</option>
                                        <option value="datetime">DATETIME</option>
                                        <option value="timestamp">TIMESTAMP</option>
                                        <option value="date">DATE</option>
                                        <option value="time">TIME</option>
                                        <option value="year">YEAR</option>
                                        <option value="boolean">BOOLEAN</option>
                                        <option value="decimal">DECIMAL</option>
                                        <option value="float">FLOAT</option>
                                        <option value="double">DOUBLE</option>
                                        <option value="json">JSON</option>
                                        <option value="blob">BLOB</option>
                                        <option value="longblob">LONGBLOB</option>
                                        <option value="mediumblob">MEDIUMBLOB</option>
                                        <option value="tinyblob">TINYBLOB</option>
                                        <option value="enum">ENUM</option>
                                        <option value="set">SET</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Length</label>
                                    <input type="number" class="form-control column-length" name="columns[0][length]">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Default</label>
                                    <input type="text" class="form-control" name="columns[0][default_value]">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Options</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="columns[0][is_primary]" checked>
                                        <label class="form-check-label">Primary</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="columns[0][is_auto_increment]" checked>
                                        <label class="form-check-label">Auto Inc</label>
                                    </div>
                                </div>
                                <div class="col-md-1">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-danger btn-sm d-block" onclick="removeColumn(this)" disabled>
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <button type="button" class="btn btn-secondary mb-3" onclick="addColumn()">
                        <i class="fas fa-plus me-1"></i>Add Column
                    </button>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="createTable()">Create Table</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Table Modal -->
<div class="modal fade" id="editTableModal" tabindex="-1" aria-labelledby="editTableModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editTableModalLabel">Edit Table</h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editTableForm">
                    <input type="hidden" id="editTableId" name="id">
                    <!-- Same form fields as create but without columns -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editDisplayName" class="form-label">Display Name</label>
                                <input type="text" class="form-control" id="editDisplayName" name="display_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editIcon" class="form-label">Icon</label>
                                <input type="text" class="form-control" id="editIcon" name="icon">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="editDescription" name="description" rows="2"></textarea>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="editIsActive" name="is_active">
                        <label class="form-check-label" for="editIsActive">
                            Active (show in admin panel)
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="updateTable()">Update Table</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">

<script>
let columnIndex = 1;

$(document).ready(function() {
    // Initialize DataTable
    $('#tables-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '/admin/tables/data',
            type: 'GET'
        },
        columns: [
            { data: 'id' },
            { data: 'name' },
            { data: 'display_name' },
            { data: 'description' },
            { data: 'columns_count' },
            { data: 'relations_count' },
            { 
                data: 'is_active',
                render: function(data) {
                    return data ? '<span class="badge bg-success">Active</span>' : '<span class="badge bg-secondary">Inactive</span>';
                }
            },
            { data: 'created_at' },
            { 
                data: 'actions',
                orderable: false,
                searchable: false
            }
        ],
        order: [[0, 'desc']],
        pageLength: 25
    });
});

// Auto-generate display name and model name from table name
document.getElementById('tableName').addEventListener('input', function() {
    const tableName = this.value;
    const displayName = tableName.split('_').map(word => 
        word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
    const modelName = tableName.split('_').map(word => 
        word.charAt(0).toUpperCase() + word.slice(1)
    ).join('');
    
    document.getElementById('displayName').value = displayName;
    document.getElementById('modelName').value = modelName;
});

// Add new column
function addColumn() {
    const container = document.getElementById('columnsContainer');
    const columnRow = document.createElement('div');
    columnRow.className = 'column-row border rounded p-3 mb-3';
    columnRow.innerHTML = `
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">Column Name *</label>
                <input type="text" class="form-control column-name" name="columns[${columnIndex}][name]" required>
            </div>
            <div class="col-md-2">
                <label class="form-label">Type *</label>
                <select class="form-select column-type" name="columns[${columnIndex}][type]" required>
                    <option value="varchar" selected>VARCHAR</option>
                    <option value="int">INT</option>
                    <option value="bigint">BIGINT</option>
                    <option value="smallint">SMALLINT</option>
                    <option value="tinyint">TINYINT</option>
                    <option value="char">CHAR</option>
                    <option value="text">TEXT</option>
                    <option value="longtext">LONGTEXT</option>
                    <option value="mediumtext">MEDIUMTEXT</option>
                    <option value="tinytext">TINYTEXT</option>
                    <option value="datetime">DATETIME</option>
                    <option value="timestamp">TIMESTAMP</option>
                    <option value="date">DATE</option>
                    <option value="time">TIME</option>
                    <option value="year">YEAR</option>
                    <option value="boolean">BOOLEAN</option>
                    <option value="decimal">DECIMAL</option>
                    <option value="float">FLOAT</option>
                    <option value="double">DOUBLE</option>
                    <option value="json">JSON</option>
                    <option value="blob">BLOB</option>
                    <option value="longblob">LONGBLOB</option>
                    <option value="mediumblob">MEDIUMBLOB</option>
                    <option value="tinyblob">TINYBLOB</option>
                    <option value="enum">ENUM</option>
                    <option value="set">SET</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">Length</label>
                <input type="number" class="form-control column-length" name="columns[${columnIndex}][length]" value="255">
            </div>
            <div class="col-md-2">
                <label class="form-label">Default</label>
                <input type="text" class="form-control" name="columns[${columnIndex}][default_value]">
            </div>
            <div class="col-md-2">
                <label class="form-label">Options</label>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="columns[${columnIndex}][is_nullable]" checked>
                    <label class="form-check-label">Nullable</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="columns[${columnIndex}][is_unique]">
                    <label class="form-check-label">Unique</label>
                </div>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <button type="button" class="btn btn-danger btn-sm d-block" onclick="removeColumn(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    container.appendChild(columnRow);
    columnIndex++;
}

// Remove column
function removeColumn(button) {
    button.closest('.column-row').remove();
}

// Sync tables from database
async function syncTables() {
    try {
        const response = await fetch('/admin/sync-tables', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            let message = result.message;

            // Hiển thị chi tiết nếu có thay đổi
            if (result.added > 0 || result.deleted > 0) {
                const details = [];
                if (result.added > 0) {
                    const addedTables = result.results.filter(r => r.action === 'created').map(r => r.table);
                    details.push(`➕ Added ${result.added} table(s): ${addedTables.join(', ')}`);
                }
                if (result.deleted > 0) {
                    const deletedTables = result.results.filter(r => r.action === 'deleted').map(r => r.table);
                    details.push(`➖ Removed ${result.deleted} table(s): ${deletedTables.join(', ')}`);
                }
                if (result.exists > 0) {
                    details.push(`✅ ${result.exists} table(s) already exist`);
                }

                message = `Tables synced successfully!\n\n${details.join('\n')}`;
            }

            alert(message);
            $('#tables-table').DataTable().ajax.reload();
        } else {
            alert('Error syncing tables: ' + result.message);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error syncing tables');
    }
}

// Sync tables from database and create migrations
async function syncTablesWithMigrations() {
    try {
        if (!confirm('This will sync tables and create Prisma migrations for existing tables. This is useful for production deployment. Continue?')) {
            return;
        }

        const response = await fetch('/admin/sync-tables-with-migrations', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            let message = result.message;
            if (result.data) {
                const { sync, migrations } = result.data;
                message = `Sync with migrations completed!\n\n` +
                         `Tables deleted: ${sync.deleted}\n` +
                         `Tables added: ${sync.added}\n` +
                         `Migrations created: ${migrations.synced}`;
                
                if (migrations.results && migrations.results.length > 0) {
                    const failedMigrations = migrations.results.filter(r => !r.success);
                    if (failedMigrations.length > 0) {
                        message += `\n\nFailed migrations: ${failedMigrations.map(f => f.table).join(', ')}`;
                    }
                }
            }
            alert(message);
            $('#tables-table').DataTable().ajax.reload();
        } else {
            alert('Error syncing tables with migrations: ' + result.message);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error syncing tables with migrations');
    }
}

// Create new table
async function createTable() {
    // Kiểm tra tên cột hợp lệ trước khi gửi lên server
    const columnRows = document.querySelectorAll('.column-row');
    for (let row of columnRows) {
        const nameInput = row.querySelector('.column-name');
        if (nameInput) {
            const value = nameInput.value.trim();
            // Chỉ cho phép chữ cái, số, gạch dưới, không dấu, không khoảng trắng
            if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(value)) {
                alert('Tên cột "' + value + '" không hợp lệ!\nChỉ dùng chữ cái tiếng Anh, số, và dấu gạch dưới, không dấu, không khoảng trắng.');
                nameInput.focus();
                return;
            }
        }
    }

    try {
        const form = document.getElementById('createTableForm');
        const formData = new FormData(form);

        // Process form data
        const data = {
            name: formData.get('name'),
            display_name: formData.get('display_name'),
            description: formData.get('description'),
            model_name: formData.get('model_name'),
            icon: formData.get('icon'),
            order_index: parseInt(formData.get('order_index')) || 0,
            is_active: document.getElementById('isActive').checked,
            columns: []
        };

        // Process columns
        const columnRows = document.querySelectorAll('.column-row');
        columnRows.forEach((row, index) => {
            const column = {
                name: row.querySelector(`[name="columns[${index}][name]"]`).value,
                display_name: row.querySelector(`[name="columns[${index}][name]"]`).value.split('_').map(word =>
                    word.charAt(0).toUpperCase() + word.slice(1)
                ).join(' '),
                type: row.querySelector(`[name="columns[${index}][type]"]`).value,
                length: parseInt(row.querySelector(`[name="columns[${index}][length]"]`).value) || null,
                default_value: row.querySelector(`[name="columns[${index}][default_value]"]`).value || null,
                is_nullable: row.querySelector(`[name="columns[${index}][is_nullable]"]`)?.checked || false,
                is_primary: row.querySelector(`[name="columns[${index}][is_primary]"]`)?.checked || false,
                is_unique: row.querySelector(`[name="columns[${index}][is_unique]"]`)?.checked || false,
                is_auto_increment: row.querySelector(`[name="columns[${index}][is_auto_increment]"]`)?.checked || false,
                order_index: index
            };
            data.columns.push(column);
        });

        const response = await fetch('/admin/tables', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            alert('Table created successfully!');
            $('#createTableModal').modal('hide');
            $('#tables-table').DataTable().ajax.reload();
            form.reset();
        } else {
            alert('Error creating table: ' + result.message);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error creating table');
    }
}

// Edit table
function editTable(id) {
    fetch(`/admin/tables/${id}`)
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                const table = result.data;
                document.getElementById('editTableId').value = table.id;
                document.getElementById('editDisplayName').value = table.display_name;
                document.getElementById('editDescription').value = table.description || '';
                document.getElementById('editIcon').value = table.icon || '';
                document.getElementById('editIsActive').checked = table.is_active;

                $('#editTableModal').modal('show');
            } else {
                alert('Error loading table: ' + result.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading table');
        });
}

// Update table
async function updateTable() {
    try {
        const form = document.getElementById('editTableForm');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);
        data.is_active = document.getElementById('editIsActive').checked;

        const id = document.getElementById('editTableId').value;

        const response = await fetch(`/admin/tables/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            alert('Table updated successfully!');
            $('#editTableModal').modal('hide');
            $('#tables-table').DataTable().ajax.reload();
        } else {
            alert('Error updating table: ' + result.message);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error updating table');
    }
}

// Delete table
function deleteTable(id) {
    if (confirm('Are you sure you want to delete this table? This action cannot be undone and will also delete the actual database table.')) {
        fetch(`/admin/tables/${id}`, {
            method: 'DELETE',
            headers: {}
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                alert('Table deleted successfully!');
                $('#tables-table').DataTable().ajax.reload();
            } else {
                alert('Error deleting table: ' + result.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting table');
        });
    }
}

// View table structure
function viewTable(id) {
    window.location.href = `/admin/tables/${id}/structure`;
}

// Manage table data
function manageData(id) {
    window.location.href = `/admin/tables/${id}/data`;
}

// Event delegation for dynamically created buttons
$(document).on('click', '.edit-table', function() {
    const id = $(this).data('id');
    editTable(id);
});

$(document).on('click', '.delete-table', function() {
    const id = $(this).data('id');
    deleteTable(id);
});

$(document).on('click', '.view-table', function() {
    const id = $(this).data('id');
    viewTable(id);
});

$(document).on('click', '.manage-data', function() {
    const id = $(this).data('id');
    manageData(id);
});
</script>
