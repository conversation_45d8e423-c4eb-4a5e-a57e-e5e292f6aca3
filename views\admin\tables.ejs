<div class="container-lg px-4">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Database Tables Management</h5>
            <div>
                <button type="button" class="btn btn-success me-2" onclick="syncTables()">
                    <i class="fas fa-sync me-1"></i>Sync Tables
                </button>
                <button type="button" class="btn btn-primary" data-coreui-toggle="modal" data-coreui-target="#createTableModal">
                    <i class="fas fa-plus me-1"></i>Create Table
                </button>
            </div>
        </div>
        <div class="card-body">
            <table id="tables-table" class="table table-striped table-bordered" style="width:100%">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Table Name</th>
                        <th>Display Name</th>
                        <th>Description</th>
                        <th>Columns</th>
                        <th>Relations</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Create Table Modal -->
<div class="modal fade" id="createTableModal" tabindex="-1" aria-labelledby="createTableModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createTableModalLabel">Create New Table</h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="createTableForm">
                    <!-- Basic Info -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="tableName" class="form-label">Table Name *</label>
                                <input type="text" class="form-control" id="tableName" name="name" required>
                                <div class="form-text">Use lowercase with underscores (e.g., user_profiles)</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="displayName" class="form-label">Display Name *</label>
                                <input type="text" class="form-control" id="displayName" name="display_name" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label for="description" class="form-label">Description</label>
                                <textarea class="form-control" id="description" name="description" rows="2"></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="modelName" class="form-label">Model Name *</label>
                                <input type="text" class="form-control" id="modelName" name="model_name" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="icon" class="form-label">Icon</label>
                                <input type="text" class="form-control" id="icon" name="icon" placeholder="fas fa-table">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="orderIndex" class="form-label">Order Index</label>
                                <input type="number" class="form-control" id="orderIndex" name="order_index" value="0">
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-check mb-4">
                        <input class="form-check-input" type="checkbox" id="isActive" name="is_active" checked>
                        <label class="form-check-label" for="isActive">
                            Active (show in admin panel)
                        </label>
                    </div>

                    <!-- Columns Section -->
                    <h6>Table Columns</h6>
                    <div id="columnsContainer">
                        <div class="column-row border rounded p-3 mb-3">
                            <div class="row">
                                <div class="col-md-3">
                                    <label class="form-label">Column Name *</label>
                                    <input type="text" class="form-control column-name" name="columns[0][name]" value="id" required>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Type *</label>
                                    <select class="form-select column-type" name="columns[0][type]" required>
                                        <option value="int" selected>INT</option>
                                        <option value="bigint">BIGINT</option>
                                        <option value="smallint">SMALLINT</option>
                                        <option value="tinyint">TINYINT</option>
                                        <option value="varchar">VARCHAR</option>
                                        <option value="char">CHAR</option>
                                        <option value="text">TEXT</option>
                                        <option value="longtext">LONGTEXT</option>
                                        <option value="mediumtext">MEDIUMTEXT</option>
                                        <option value="tinytext">TINYTEXT</option>
                                        <option value="datetime">DATETIME</option>
                                        <option value="timestamp">TIMESTAMP</option>
                                        <option value="date">DATE</option>
                                        <option value="time">TIME</option>
                                        <option value="year">YEAR</option>
                                        <option value="boolean">BOOLEAN</option>
                                        <option value="decimal">DECIMAL</option>
                                        <option value="float">FLOAT</option>
                                        <option value="double">DOUBLE</option>
                                        <option value="json">JSON</option>
                                        <option value="blob">BLOB</option>
                                        <option value="longblob">LONGBLOB</option>
                                        <option value="mediumblob">MEDIUMBLOB</option>
                                        <option value="tinyblob">TINYBLOB</option>
                                        <option value="enum">ENUM</option>
                                        <option value="set">SET</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Length</label>
                                    <input type="number" class="form-control column-length" name="columns[0][length]">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Default</label>
                                    <input type="text" class="form-control" name="columns[0][default_value]">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">Options</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="columns[0][is_primary]" checked>
                                        <label class="form-check-label">Primary</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="columns[0][is_auto_increment]" checked>
                                        <label class="form-check-label">Auto Inc</label>
                                    </div>
                                </div>
                                <div class="col-md-1">
                                    <label class="form-label">&nbsp;</label>
                                    <button type="button" class="btn btn-danger btn-sm d-block" onclick="removeColumn(this)" disabled>
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <button type="button" class="btn btn-secondary mb-3" onclick="addColumn()">
                        <i class="fas fa-plus me-1"></i>Add Column
                    </button>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="createTable()">Create Table</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Table Modal -->
<div class="modal fade" id="editTableModal" tabindex="-1" aria-labelledby="editTableModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editTableModalLabel">Edit Table</h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editTableForm">
                    <input type="hidden" id="editTableId" name="id">
                    <!-- Same form fields as create but without columns -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editDisplayName" class="form-label">Display Name</label>
                                <input type="text" class="form-control" id="editDisplayName" name="display_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editIcon" class="form-label">Icon</label>
                                <input type="text" class="form-control" id="editIcon" name="icon">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="editDescription" name="description" rows="2"></textarea>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="editIsActive" name="is_active">
                        <label class="form-check-label" for="editIsActive">
                            Active (show in admin panel)
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="updateTable()">Update Table</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">

<script>
let columnIndex = 1;

$(document).ready(function() {
    // Initialize DataTable
    $('#tables-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '/admin/tables/data',
            type: 'GET'
        },
        columns: [
            { data: 'id' },
            { data: 'name' },
            { data: 'display_name' },
            { data: 'description' },
            { data: 'columns_count' },
            { data: 'relations_count' },
            {
                data: 'is_active',
                render: function(data) {
                    return data ? '<span class="badge bg-success">Active</span>' : '<span class="badge bg-secondary">Inactive</span>';
                }
            },
            { data: 'created_at' },
            {
                data: 'actions',
                orderable: false,
                searchable: false
            }
        ],
        order: [[0, 'desc']],
        pageLength: 25
    });

    // Real-time validation for table name
    $(document).on('input', '[name="name"]', function() {
        const value = $(this).val();
        const error = validateTableName(value);

        if (error) {
            $(this).addClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
            $(this).after(`<div class="invalid-feedback">${error}</div>`);
        } else {
            $(this).removeClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
        }
    });

    // Real-time validation for display name
    $(document).on('input', '[name="display_name"]', function() {
        const value = $(this).val();
        const error = validateDisplayName(value);

        if (error) {
            $(this).addClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
            $(this).after(`<div class="invalid-feedback">${error}</div>`);
        } else {
            $(this).removeClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
        }
    });

    // Real-time validation for column names
    $(document).on('input', '.column-name', function() {
        const value = $(this).val();
        const error = validateColumnName(value);

        if (error) {
            $(this).addClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
            $(this).after(`<div class="invalid-feedback">${error}</div>`);
        } else {
            $(this).removeClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
        }
    });

    // Real-time validation for column types and lengths
    $(document).on('change', '.column-type', function() {
        const row = $(this).closest('.column-row');
        const lengthInput = row.find('.column-length');
        const type = $(this).val();

        // Show/hide length input based on type
        const typesNeedingLength = ['varchar', 'char', 'int', 'bigint', 'smallint', 'tinyint', 'mediumint', 'decimal'];
        if (typesNeedingLength.includes(type)) {
            lengthInput.show();
            lengthInput.prev('label').show();
        } else {
            lengthInput.hide();
            lengthInput.prev('label').hide();
            lengthInput.val('');
        }
    });

    $(document).on('input', '.column-length', function() {
        const row = $(this).closest('.column-row');
        const typeSelect = row.find('.column-type');
        const type = typeSelect.val();
        const length = $(this).val();

        const error = validateColumnLength(type, length);

        if (error) {
            $(this).addClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
            $(this).after(`<div class="invalid-feedback">${error}</div>`);
        } else {
            $(this).removeClass('is-invalid');
            $(this).siblings('.invalid-feedback').remove();
        }
    });
});

// Auto-generate display name and model name from table name
document.getElementById('tableName').addEventListener('input', function() {
    const tableName = this.value;
    const displayName = tableName.split('_').map(word => 
        word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
    const modelName = tableName.split('_').map(word => 
        word.charAt(0).toUpperCase() + word.slice(1)
    ).join('');
    
    document.getElementById('displayName').value = displayName;
    document.getElementById('modelName').value = modelName;
});

// Add new column
function addColumn() {
    const container = document.getElementById('columnsContainer');
    const columnRow = document.createElement('div');
    columnRow.className = 'column-row border rounded p-3 mb-3';
    columnRow.innerHTML = `
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">Column Name *</label>
                <input type="text" class="form-control column-name" name="columns[${columnIndex}][name]" required>
            </div>
            <div class="col-md-2">
                <label class="form-label">Type *</label>
                <select class="form-select column-type" name="columns[${columnIndex}][type]" required>
                    <option value="varchar" selected>VARCHAR</option>
                    <option value="int">INT</option>
                    <option value="bigint">BIGINT</option>
                    <option value="smallint">SMALLINT</option>
                    <option value="tinyint">TINYINT</option>
                    <option value="char">CHAR</option>
                    <option value="text">TEXT</option>
                    <option value="longtext">LONGTEXT</option>
                    <option value="mediumtext">MEDIUMTEXT</option>
                    <option value="tinytext">TINYTEXT</option>
                    <option value="datetime">DATETIME</option>
                    <option value="timestamp">TIMESTAMP</option>
                    <option value="date">DATE</option>
                    <option value="time">TIME</option>
                    <option value="year">YEAR</option>
                    <option value="boolean">BOOLEAN</option>
                    <option value="decimal">DECIMAL</option>
                    <option value="float">FLOAT</option>
                    <option value="double">DOUBLE</option>
                    <option value="json">JSON</option>
                    <option value="blob">BLOB</option>
                    <option value="longblob">LONGBLOB</option>
                    <option value="mediumblob">MEDIUMBLOB</option>
                    <option value="tinyblob">TINYBLOB</option>
                    <option value="enum">ENUM</option>
                    <option value="set">SET</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">Length</label>
                <input type="number" class="form-control column-length" name="columns[${columnIndex}][length]" value="255">
            </div>
            <div class="col-md-2">
                <label class="form-label">Default</label>
                <input type="text" class="form-control" name="columns[${columnIndex}][default_value]">
            </div>
            <div class="col-md-2">
                <label class="form-label">Options</label>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="columns[${columnIndex}][is_nullable]" checked>
                    <label class="form-check-label">Nullable</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" name="columns[${columnIndex}][is_unique]">
                    <label class="form-check-label">Unique</label>
                </div>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <button type="button" class="btn btn-danger btn-sm d-block" onclick="removeColumn(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    container.appendChild(columnRow);
    columnIndex++;
}

// Remove column
function removeColumn(button) {
    button.closest('.column-row').remove();
}

// Sync tables from database
async function syncTables() {
    try {
        const response = await fetch('/admin/sync-tables', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            let message = result.message;

            // Hiển thị chi tiết nếu có thay đổi
            if (result.added > 0 || result.deleted > 0) {
                const details = [];
                if (result.added > 0) {
                    const addedTables = result.results.filter(r => r.action === 'created').map(r => r.table);
                    details.push(`➕ Added ${result.added} table(s): ${addedTables.join(', ')}`);
                }
                if (result.deleted > 0) {
                    const deletedTables = result.results.filter(r => r.action === 'deleted').map(r => r.table);
                    details.push(`➖ Removed ${result.deleted} table(s): ${deletedTables.join(', ')}`);
                }
                if (result.exists > 0) {
                    details.push(`✅ ${result.exists} table(s) already exist`);
                }

                message = `Tables synced successfully!\n\n${details.join('\n')}`;
            }

            alert(message);
            $('#tables-table').DataTable().ajax.reload();
        } else {
            alert('Error syncing tables: ' + result.message);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error syncing tables');
    }
}
// Validation functions
function validateTableName(name) {
    if (!name || name.trim().length === 0) {
        return 'Table name is required';
    }

    const trimmedName = name.trim();

    if (trimmedName.length > 64) {
        return 'Table name cannot exceed 64 characters';
    }

    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(trimmedName)) {
        return 'Table name can only contain letters, numbers, and underscores. Must start with a letter or underscore';
    }

    const reservedWords = [
        'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER', 'INDEX',
        'TABLE', 'DATABASE', 'SCHEMA', 'VIEW', 'TRIGGER', 'PROCEDURE', 'FUNCTION',
        'USER', 'ROLE', 'GRANT', 'REVOKE', 'ORDER', 'GROUP', 'HAVING', 'WHERE',
        'FROM', 'JOIN', 'INNER', 'LEFT', 'RIGHT', 'OUTER', 'UNION', 'DISTINCT',
        'COUNT', 'SUM', 'AVG', 'MIN', 'MAX', 'AND', 'OR', 'NOT', 'NULL', 'TRUE', 'FALSE'
    ];

    if (reservedWords.includes(trimmedName.toUpperCase())) {
        return `"${trimmedName}" is a reserved MySQL keyword and cannot be used as table name`;
    }

    return null;
}

function validateColumnName(name) {
    if (!name || name.trim().length === 0) {
        return 'Column name is required';
    }

    const trimmedName = name.trim();

    if (trimmedName.length > 64) {
        return 'Column name cannot exceed 64 characters';
    }

    if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(trimmedName)) {
        return 'Column name can only contain letters, numbers, and underscores. Must start with a letter or underscore';
    }

    const reservedWords = [
        'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'CREATE', 'DROP', 'ALTER', 'INDEX',
        'TABLE', 'DATABASE', 'SCHEMA', 'VIEW', 'TRIGGER', 'PROCEDURE', 'FUNCTION',
        'USER', 'ROLE', 'GRANT', 'REVOKE', 'ORDER', 'GROUP', 'HAVING', 'WHERE',
        'FROM', 'JOIN', 'INNER', 'LEFT', 'RIGHT', 'OUTER', 'UNION', 'DISTINCT',
        'COUNT', 'SUM', 'AVG', 'MIN', 'MAX', 'AND', 'OR', 'NOT', 'NULL', 'TRUE', 'FALSE',
        'KEY', 'PRIMARY', 'FOREIGN', 'UNIQUE', 'AUTO_INCREMENT', 'DEFAULT'
    ];

    if (reservedWords.includes(trimmedName.toUpperCase())) {
        return `"${trimmedName}" is a reserved MySQL keyword and cannot be used as column name`;
    }

    return null;
}

function validateDisplayName(name) {
    if (!name || name.trim().length === 0) {
        return 'Display name is required';
    }

    if (name.trim().length > 255) {
        return 'Display name cannot exceed 255 characters';
    }

    return null;
}

function validateColumnType(type) {
    const validTypes = [
        'int', 'bigint', 'smallint', 'tinyint', 'mediumint',
        'varchar', 'char', 'text', 'mediumtext', 'longtext',
        'decimal', 'float', 'double',
        'date', 'datetime', 'timestamp', 'time', 'year',
        'boolean', 'tinyint(1)',
        'json', 'blob', 'mediumblob', 'longblob'
    ];

    if (!validTypes.includes(type.toLowerCase())) {
        return `Invalid column type: ${type}`;
    }

    return null;
}

function validateColumnLength(type, length) {
    if (!length) return null;

    const numericTypes = ['int', 'bigint', 'smallint', 'tinyint', 'mediumint'];
    const stringTypes = ['varchar', 'char'];
    const decimalTypes = ['decimal'];

    if (numericTypes.includes(type)) {
        const num = parseInt(length);
        if (isNaN(num) || num < 1 || num > 255) {
            return 'Numeric type length must be between 1 and 255';
        }
    }

    if (stringTypes.includes(type)) {
        const num = parseInt(length);
        if (isNaN(num) || num < 1 || num > 65535) {
            return 'String type length must be between 1 and 65535';
        }
    }

    if (decimalTypes.includes(type)) {
        if (!/^\d+,\d+$/.test(length)) {
            return 'Decimal length must be in format "precision,scale" (e.g., "10,2")';
        }
        const [precision, scale] = length.split(',').map(n => parseInt(n));
        if (precision < 1 || precision > 65 || scale < 0 || scale > 30 || scale > precision) {
            return 'Invalid decimal precision/scale. Precision: 1-65, Scale: 0-30, Scale <= Precision';
        }
    }

    return null;
}

// Create new table
async function createTable() {
    const errors = [];

    // Validate table name
    const tableName = document.querySelector('[name="name"]').value;
    const tableNameError = validateTableName(tableName);
    if (tableNameError) {
        errors.push(`Table name: ${tableNameError}`);
    }

    // Validate display name
    const displayName = document.querySelector('[name="display_name"]').value;
    const displayNameError = validateDisplayName(displayName);
    if (displayNameError) {
        errors.push(`Display name: ${displayNameError}`);
    }

    // Validate columns
    const columnRows = document.querySelectorAll('.column-row');
    const columnNames = [];
    let hasPrimaryKey = false;

    for (let i = 0; i < columnRows.length; i++) {
        const row = columnRows[i];
        const nameInput = row.querySelector('.column-name');
        const typeSelect = row.querySelector('.column-type');
        const lengthInput = row.querySelector('.column-length');
        const isPrimaryCheckbox = row.querySelector('.column-primary');

        if (nameInput && typeSelect) {
            const columnName = nameInput.value.trim();
            const columnType = typeSelect.value;
            const columnLength = lengthInput ? lengthInput.value.trim() : '';
            const isPrimary = isPrimaryCheckbox ? isPrimaryCheckbox.checked : false;

            // Validate column name
            const columnNameError = validateColumnName(columnName);
            if (columnNameError) {
                errors.push(`Column ${i + 1} name: ${columnNameError}`);
            } else {
                // Check for duplicate column names
                if (columnNames.includes(columnName.toLowerCase())) {
                    errors.push(`Duplicate column name: ${columnName}`);
                } else {
                    columnNames.push(columnName.toLowerCase());
                }
            }

            // Validate column type
            const columnTypeError = validateColumnType(columnType);
            if (columnTypeError) {
                errors.push(`Column ${i + 1} type: ${columnTypeError}`);
            }

            // Validate column length
            const columnLengthError = validateColumnLength(columnType, columnLength);
            if (columnLengthError) {
                errors.push(`Column ${i + 1} length: ${columnLengthError}`);
            }

            // Check for primary key
            if (isPrimary) {
                hasPrimaryKey = true;
            }
        }
    }

    // Check if at least one primary key exists
    if (!hasPrimaryKey) {
        errors.push('At least one column must be marked as primary key');
    }

    // Show errors if any
    if (errors.length > 0) {
        alert('Validation errors:\n\n' + errors.join('\n'));
        return;
    }

    try {
        const form = document.getElementById('createTableForm');
        const formData = new FormData(form);

        // Process form data
        const data = {
            name: formData.get('name'),
            display_name: formData.get('display_name'),
            description: formData.get('description'),
            model_name: formData.get('model_name'),
            icon: formData.get('icon'),
            order_index: parseInt(formData.get('order_index')) || 0,
            is_active: document.getElementById('isActive').checked,
            columns: []
        };

        // Process columns
        const columnRows = document.querySelectorAll('.column-row');
        columnRows.forEach((row, index) => {
            const column = {
                name: row.querySelector(`[name="columns[${index}][name]"]`).value,
                display_name: row.querySelector(`[name="columns[${index}][name]"]`).value.split('_').map(word =>
                    word.charAt(0).toUpperCase() + word.slice(1)
                ).join(' '),
                type: row.querySelector(`[name="columns[${index}][type]"]`).value,
                length: parseInt(row.querySelector(`[name="columns[${index}][length]"]`).value) || null,
                default_value: row.querySelector(`[name="columns[${index}][default_value]"]`).value || null,
                is_nullable: row.querySelector(`[name="columns[${index}][is_nullable]"]`)?.checked || false,
                is_primary: row.querySelector(`[name="columns[${index}][is_primary]"]`)?.checked || false,
                is_unique: row.querySelector(`[name="columns[${index}][is_unique]"]`)?.checked || false,
                is_auto_increment: row.querySelector(`[name="columns[${index}][is_auto_increment]"]`)?.checked || false,
                order_index: index
            };
            data.columns.push(column);
        });

        const response = await fetch('/admin/tables', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            alert('Table created successfully!');
            $('#createTableModal').modal('hide');
            $('#tables-table').DataTable().ajax.reload();
            form.reset();
        } else {
            let errorMessage = 'Error creating table: ' + result.message;

            // Display detailed validation errors if available
            if (result.errors && result.errors.length > 0) {
                const errorDetails = result.errors.map(err => {
                    return err.field ? `${err.field}: ${err.message}` : err.message;
                }).join('\n');
                errorMessage += '\n\nValidation errors:\n' + errorDetails;
            }

            alert(errorMessage);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error creating table');
    }
}

// Edit table
function editTable(id) {
    fetch(`/admin/tables/${id}`)
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                const table = result.data;
                document.getElementById('editTableId').value = table.id;
                document.getElementById('editDisplayName').value = table.display_name;
                document.getElementById('editDescription').value = table.description || '';
                document.getElementById('editIcon').value = table.icon || '';
                document.getElementById('editIsActive').checked = table.is_active;

                $('#editTableModal').modal('show');
            } else {
                alert('Error loading table: ' + result.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading table');
        });
}

// Update table
async function updateTable() {
    try {
        const form = document.getElementById('editTableForm');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);
        data.is_active = document.getElementById('editIsActive').checked;

        const id = document.getElementById('editTableId').value;

        const response = await fetch(`/admin/tables/${id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        if (result.success) {
            alert('Table updated successfully!');
            $('#editTableModal').modal('hide');
            $('#tables-table').DataTable().ajax.reload();
        } else {
            alert('Error updating table: ' + result.message);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error updating table');
    }
}

// Delete table
function deleteTable(id) {
    if (confirm('Are you sure you want to delete this table? This action cannot be undone and will also delete the actual database table.')) {
        fetch(`/admin/tables/${id}`, {
            method: 'DELETE',
            headers: {}
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                alert('Table deleted successfully!');
                $('#tables-table').DataTable().ajax.reload();
            } else {
                alert('Error deleting table: ' + result.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting table');
        });
    }
}

// View table structure
function viewTable(id) {
    window.location.href = `/admin/tables/${id}/structure`;
}

// Manage table data
function manageData(id) {
    window.location.href = `/admin/tables/${id}/data`;
}

// Event delegation for dynamically created buttons
$(document).on('click', '.edit-table', function() {
    const id = $(this).data('id');
    editTable(id);
});

$(document).on('click', '.delete-table', function() {
    const id = $(this).data('id');
    deleteTable(id);
});

$(document).on('click', '.view-table', function() {
    const id = $(this).data('id');
    viewTable(id);
});

$(document).on('click', '.manage-data', function() {
    const id = $(this).data('id');
    manageData(id);
});
</script>
