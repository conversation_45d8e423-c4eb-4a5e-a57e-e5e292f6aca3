const mysql = require('mysql2/promise');

async function quickFix() {
  let connection;
  
  try {
    console.log('🔧 Quick fix for login data...\n');

    // Tạo connection trực tiếp
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'coreui'
    });

    console.log('✅ Connected to database\n');

    // Kiểm tra users hiện tại
    console.log('1. Checking current users...');
    const [users] = await connection.execute('SELECT id, email FROM user');
    console.log(`Found ${users.length} users`);

    // Kiểm tra roles hiện tại
    console.log('2. Checking current roles...');
    const [roles] = await connection.execute('SELECT id, name FROM role');
    console.log(`Found ${roles.length} roles`);

    // Tạo admin role nếu chưa có
    console.log('3. Creating admin role...');
    try {
      await connection.execute(`
        INSERT IGNORE INTO role (id, name, description, created_at, updated_at) 
        VALUES (1, 'admin', 'Administrator role', NOW(), NOW())
      `);
      console.log('✅ Admin role ensured');
    } catch (error) {
      console.log('⚠️ Admin role already exists or error:', error.message);
    }

    // Tạo admin user nếu chưa có
    console.log('4. Creating admin user...');
    try {
      await connection.execute(`
        INSERT IGNORE INTO user (id, name, email, password, email_verified_at, created_at, updated_at) 
        VALUES (1, 'Admin User', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', NOW(), NOW(), NOW())
      `);
      console.log('✅ Admin user ensured');
    } catch (error) {
      console.log('⚠️ Admin user already exists or error:', error.message);
    }

    // Tạo role_user relationship
    console.log('5. Creating admin role-user relationship...');
    try {
      await connection.execute(`
        INSERT IGNORE INTO role_user (user_id, role_id, created_at, updated_at) 
        VALUES (1, 1, NOW(), NOW())
      `);
      console.log('✅ Admin role-user relationship ensured');
    } catch (error) {
      console.log('⚠️ Relationship already exists or error:', error.message);
    }

    // Kiểm tra kết quả
    console.log('\n6. Final verification...');
    const [finalUsers] = await connection.execute('SELECT id, email FROM user');
    const [finalRoles] = await connection.execute('SELECT id, name FROM role');
    const [finalRoleUsers] = await connection.execute('SELECT user_id, role_id FROM role_user');

    console.log(`📊 Final counts:`);
    console.log(`   - Users: ${finalUsers.length}`);
    console.log(`   - Roles: ${finalRoles.length}`);
    console.log(`   - Role-User relationships: ${finalRoleUsers.length}`);

    if (finalUsers.length > 0 && finalRoles.length > 0 && finalRoleUsers.length > 0) {
      console.log('\n✅ Login data fixed successfully!');
      console.log('\n🔐 You can now login with:');
      console.log('   📧 Email: <EMAIL>');
      console.log('   🔑 Password: admin123');
    } else {
      console.log('\n❌ Still missing some data. Please run the SQL file manually.');
    }

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.log('\n💡 If this fails, please run the SQL commands manually:');
    console.log('   1. Open your MySQL client');
    console.log('   2. Connect to your database');
    console.log('   3. Run the commands in fix-login.sql');
  } finally {
    if (connection) {
      await connection.end();
    }
    process.exit(0);
  }
}

quickFix();
