<div class="container-lg px-4">
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="<%= table.icon || 'fas fa-table' %> me-2"></i>
                <%= table.display_name %> Data Management
            </h5>
            <div>
                <button type="button" class="btn btn-primary" data-coreui-toggle="modal" data-coreui-target="#addRecordModal" onclick="openAddModal()">
                    <i class="fas fa-plus me-1"></i>Add <%= table.display_name.slice(0, -1) %>
                </button>
            </div>
        </div>
        <div class="card-body">
            <table id="data-table" class="table table-striped table-bordered" style="width:100%">
                <thead>
                    <tr>
                        <% table.columns.filter(col => col.is_visible_list).forEach(column => { %>
                            <th><%= column.display_name %></th>
                        <% }); %>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Record Modal -->
<div class="modal fade" id="addRecordModal" tabindex="-1" aria-labelledby="addRecordModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addRecordModalLabel">Add <%= table.display_name.slice(0, -1) %></h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addRecordForm">
                    <% table.columns.filter(col => col.is_visible_form && col.name !== 'id').forEach(column => { %>
                        <%
                        // Check if this column has a foreign key relation
                        const relation = table.relations.find(rel => rel.column.name === column.name);
                        %>
                        <div class="mb-3">
                            <label for="add_<%= column.name %>" class="form-label">
                                <%= column.display_name %>
                                <% if (column.validation_rules && JSON.parse(column.validation_rules).required) { %>
                                    <span class="text-danger">*</span>
                                <% } %>
                            </label>

                            <% if (relation) { %>
                                <!-- Foreign Key Dropdown -->
                                <select class="form-select foreign-key-select"
                                        id="add_<%= column.name %>"
                                        name="<%= column.name %>"
                                        data-foreign-table="<%= relation.foreign_table.name %>"
                                        data-foreign-column="<%= relation.foreign_column %>"
                                        data-display-column="<%= relation.display_column %>"
                                        <%= column.validation_rules && JSON.parse(column.validation_rules).required ? 'required' : '' %>>
                                    <option value="">Select <%= column.display_name %></option>
                                    <!-- Options will be loaded via AJAX -->
                                </select>
                            <% } else if (column.form_type === 'select') { %>
                                <% const rules = column.validation_rules ? JSON.parse(column.validation_rules) : {}; %>
                                <% if (rules.options) { %>
                                    <select class="form-select" id="add_<%= column.name %>" name="<%= column.name %>"
                                            <%= rules.required ? 'required' : '' %>>
                                        <option value="">Select <%= column.display_name %></option>
                                        <% rules.options.forEach(option => { %>
                                            <option value="<%= option.value %>"><%= option.label %></option>
                                        <% }); %>
                                    </select>
                                <% } %>
                            <% } else if (column.form_type === 'textarea') { %>
                                <textarea class="form-control" id="add_<%= column.name %>" name="<%= column.name %>"
                                         rows="3" <%= column.validation_rules && JSON.parse(column.validation_rules).required ? 'required' : '' %>></textarea>
                            <% } else if (column.form_type === 'checkbox') { %>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="add_<%= column.name %>" name="<%= column.name %>" value="1">
                                    <label class="form-check-label" for="add_<%= column.name %>">
                                        <%= column.display_name %>
                                    </label>
                                </div>
                            <% } else { %>
                                <input type="<%= column.form_type %>" class="form-control" id="add_<%= column.name %>"
                                       name="<%= column.name %>" <%= column.validation_rules && JSON.parse(column.validation_rules).required ? 'required' : '' %>>
                            <% } %>
                            
                            <% if (column.validation_rules) { %>
                                <% const rules = JSON.parse(column.validation_rules); %>
                                <% if (rules.minLength || rules.maxLength) { %>
                                    <div class="form-text">
                                        <% if (rules.minLength) { %>Minimum <%= rules.minLength %> characters. <% } %>
                                        <% if (rules.maxLength) { %>Maximum <%= rules.maxLength %> characters.<% } %>
                                    </div>
                                <% } %>
                            <% } %>
                        </div>
                    <% }); %>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="addRecord()">Add Record</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Record Modal -->
<div class="modal fade" id="editRecordModal" tabindex="-1" aria-labelledby="editRecordModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editRecordModalLabel">Edit <%= table.display_name.slice(0, -1) %></h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editRecordForm">
                    <input type="hidden" id="editRecordId" name="id">
                    <% table.columns.filter(col => col.is_visible_form && col.name !== 'id').forEach(column => { %>
                        <%
                        // Check if this column has a foreign key relation
                        const relation = table.relations.find(rel => rel.column.name === column.name);
                        %>
                        <div class="mb-3">
                            <label for="edit_<%= column.name %>" class="form-label">
                                <%= column.display_name %>
                                <% if (column.validation_rules && JSON.parse(column.validation_rules).required) { %>
                                    <span class="text-danger">*</span>
                                <% } %>
                            </label>

                            <% if (relation) { %>
                                <!-- Foreign Key Dropdown -->
                                <select class="form-select foreign-key-select"
                                        id="edit_<%= column.name %>"
                                        name="<%= column.name %>"
                                        data-foreign-table="<%= relation.foreign_table.name %>"
                                        data-foreign-column="<%= relation.foreign_column %>"
                                        data-display-column="<%= relation.display_column %>"
                                        <%= column.validation_rules && JSON.parse(column.validation_rules).required ? 'required' : '' %>>
                                    <option value="">Select <%= column.display_name %></option>
                                    <!-- Options will be loaded via AJAX -->
                                </select>
                            <% } else if (column.form_type === 'select') { %>
                                <% const rules = column.validation_rules ? JSON.parse(column.validation_rules) : {}; %>
                                <% if (rules.options) { %>
                                    <select class="form-select" id="edit_<%= column.name %>" name="<%= column.name %>"
                                            <%= rules.required ? 'required' : '' %>>
                                        <option value="">Select <%= column.display_name %></option>
                                        <% rules.options.forEach(option => { %>
                                            <option value="<%= option.value %>"><%= option.label %></option>
                                        <% }); %>
                                    </select>
                                <% } %>
                            <% } else if (column.form_type === 'textarea') { %>
                                <textarea class="form-control" id="edit_<%= column.name %>" name="<%= column.name %>"
                                         rows="3" <%= column.validation_rules && JSON.parse(column.validation_rules).required ? 'required' : '' %>></textarea>
                            <% } else if (column.form_type === 'checkbox') { %>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="edit_<%= column.name %>" name="<%= column.name %>" value="1">
                                    <label class="form-check-label" for="edit_<%= column.name %>">
                                        <%= column.display_name %>
                                    </label>
                                </div>
                            <% } else { %>
                                <input type="<%= column.form_type %>" class="form-control" id="edit_<%= column.name %>"
                                       name="<%= column.name %>" <%= column.validation_rules && JSON.parse(column.validation_rules).required ? 'required' : '' %>>
                            <% } %>
                        </div>
                    <% }); %>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="updateRecord()">Update Record</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">

<script>
const tableId = <%= table.id %>;
let dataTable;

$(document).ready(function() {
    // Initialize DataTable
    const columns = [
        <% table.columns.filter(col => col.is_visible_list).forEach((column, index) => { %>
            {
                data: '<%= column.name %>',
                name: '<%= column.name %>',
                title: '<%= column.display_name %>'
            },
        <% }); %>
        {
            data: 'actions',
            name: 'actions',
            title: 'Actions',
            orderable: false,
            searchable: false
        }
    ];

    dataTable = $('#data-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: `/admin/tables/${tableId}/data/api`,
            type: 'GET'
        },
        columns: columns,
        order: [[0, 'desc']],
        pageLength: 25
    });

    // Load foreign key dropdown data when modals are shown
    $('#addRecordModal').on('show.coreui.modal show.bs.modal', function() {
        console.log('Add modal opening, loading foreign key dropdowns...');
        loadForeignKeyDropdowns('add');
    });

    $('#editRecordModal').on('show.coreui.modal show.bs.modal', function() {
        console.log('Edit modal opening, loading foreign key dropdowns...');
        loadForeignKeyDropdowns('edit');
    });

    // Load dropdown data immediately on page load for testing
    console.log('Page loaded, testing foreign key dropdowns...');
    setTimeout(() => {
        console.log('Testing after 2 seconds...');
        loadForeignKeyDropdowns('add');
    }, 2000);
});

// Function to load foreign key dropdown data
async function loadForeignKeyDropdowns(prefix) {
    console.log(`Loading foreign key dropdowns for prefix: ${prefix}`);
    const selector = `#${prefix}RecordForm .foreign-key-select`;
    console.log(`Using selector: ${selector}`);
    const foreignKeySelects = document.querySelectorAll(selector);
    console.log(`Found ${foreignKeySelects.length} foreign key selects:`, foreignKeySelects);

    // Also try alternative selectors for debugging
    const allSelects = document.querySelectorAll(`#${prefix}RecordForm select`);
    console.log(`Total selects in form: ${allSelects.length}`);
    allSelects.forEach((select, index) => {
        console.log(`Select ${index}: id=${select.id}, class=${select.className}, name=${select.name}`);
    });

    const promises = [];

    for (const select of foreignKeySelects) {
        const columnName = select.name;
        const url = `/admin/tables/${tableId}/relations/${columnName}/dropdown`;
        console.log(`Loading dropdown data for ${columnName} from: ${url}`);

        const promise = fetch(url)
            .then(response => {
                console.log(`Response for ${columnName}:`, response.status, response.statusText);
                return response.json();
            })
            .then(result => {
                console.log(`Result for ${columnName}:`, result);
                if (result.success) {
                    // Clear existing options except the first one
                    const firstOption = select.querySelector('option:first-child');
                    select.innerHTML = '';
                    select.appendChild(firstOption);

                    // Add new options
                    result.data.forEach(item => {
                        const option = document.createElement('option');
                        option.value = item.value;
                        option.textContent = item.label;
                        select.appendChild(option);
                        console.log(`Added option: ${item.value} - ${item.label}`);
                    });
                    console.log(`Successfully loaded ${result.data.length} options for ${columnName}`);
                } else {
                    console.error(`Failed to load dropdown data for ${columnName}:`, result.message);
                }
            })
            .catch(error => {
                console.error(`Error loading dropdown data for ${columnName}:`, error);
            });

        promises.push(promise);
    }

    // Wait for all dropdowns to be loaded
    await Promise.all(promises);
    console.log('All foreign key dropdowns loaded');
}

// Function to open add modal and load dropdowns
async function openAddModal() {
    console.log('Opening add modal...');
    // Load foreign key dropdowns immediately
    await loadForeignKeyDropdowns('add');
}
// Add new record
async function addRecord() {
    try {
        const form = document.getElementById('addRecordForm');
        const formData = new FormData(form);
        const data = {};
        
        // Process form data
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        // Handle checkboxes
        <% table.columns.filter(col => col.is_visible_form && col.form_type === 'checkbox').forEach(column => { %>
            data['<%= column.name %>'] = document.getElementById('add_<%= column.name %>').checked ? 1 : 0;
        <% }); %>
        
        const response = await fetch(`/admin/tables/${tableId}/records`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('Record added successfully!');
            $('#addRecordModal').modal('hide');
            dataTable.ajax.reload();
            form.reset();
        } else {
            alert('Error adding record: ' + (result.errors ? result.errors.join(', ') : result.message));
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error adding record');
    }
}

// Edit record
async function editRecord(id) {
    try {
        const response = await fetch(`/admin/tables/${tableId}/records/${id}`);
        const result = await response.json();

        if (result.success) {
            const record = result.data;
            document.getElementById('editRecordId').value = record.id;

            // Load foreign key dropdowns first
            await loadForeignKeyDropdowns('edit');

            // Fill form fields
            <% table.columns.filter(col => col.is_visible_form && col.name !== 'id').forEach(column => { %>
                <% const relation = table.relations.find(rel => rel.column.name === column.name); %>
                <% if (relation) { %>
                    // Foreign key field - set the value after dropdown is loaded
                    document.getElementById('edit_<%= column.name %>').value = record['<%= column.name %>'] || '';
                <% } else if (column.form_type === 'checkbox') { %>
                    document.getElementById('edit_<%= column.name %>').checked = record['<%= column.name %>'] == 1;
                <% } else { %>
                    document.getElementById('edit_<%= column.name %>').value = record['<%= column.name %>'] || '';
                <% } %>
            <% }); %>

            $('#editRecordModal').modal('show');
        } else {
            alert('Error loading record: ' + result.message);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error loading record');
    }
}

// Update record
async function updateRecord() {
    try {
        const form = document.getElementById('editRecordForm');
        const formData = new FormData(form);
        const data = {};
        const recordId = document.getElementById('editRecordId').value;
        
        // Process form data
        for (let [key, value] of formData.entries()) {
            if (key !== 'id') {
                data[key] = value;
            }
        }
        
        // Handle checkboxes
        <% table.columns.filter(col => col.is_visible_form && col.form_type === 'checkbox').forEach(column => { %>
            data['<%= column.name %>'] = document.getElementById('edit_<%= column.name %>').checked ? 1 : 0;
        <% }); %>
        
        const response = await fetch(`/admin/tables/${tableId}/records/${recordId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('Record updated successfully!');
            $('#editRecordModal').modal('hide');
            dataTable.ajax.reload();
        } else {
            alert('Error updating record: ' + (result.errors ? result.errors.join(', ') : result.message));
        }
    } catch (error) {
        console.error('Error:', error);
        alert('Error updating record');
    }
}

// Delete record
function deleteRecord(id) {
    if (confirm('Are you sure you want to delete this record? This action cannot be undone.')) {
        fetch(`/admin/tables/${tableId}/records/${id}`, {
            method: 'DELETE',
            headers: {}
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                alert('Record deleted successfully!');
                dataTable.ajax.reload();
            } else {
                alert('Error deleting record: ' + result.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error deleting record');
        });
    }
}

// Event delegation for dynamically created buttons
$(document).on('click', '.edit-record', function() {
    const id = $(this).data('id');
    editRecord(id);
});

$(document).on('click', '.delete-record', function() {
    const id = $(this).data('id');
    deleteRecord(id);
});
</script>
