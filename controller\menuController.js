const menuService = require('../services/menuService');
const adminService = require('../services/adminService');

const menuController = {
  // Trang quản lý menu
  index: async (req, res) => {
    try {
      const breadcrumb = [
        { name: 'Home', url: '/', active: false },
        { name: 'Admin', url: '/admin', active: false },
        { name: 'Menu Management', url: '/admin/menus', active: true }
      ];

      res.render('admin/menus', {
        title: 'Menu Management',
        breadcrumb
      });
    } catch (error) {
      console.error('Error loading menu management page:', error);
      res.status(500).render('500', { layout: 'login_layout' });
    }
  },

  // API lấy danh sách menu
  getMenusData: async (req, res) => {
    try {
      const menus = await menuService.getFlatMenuList();

      const formattedMenus = menus.map(menu => ({
        id: menu.id,
        title: menu.title,
        url: menu.url || '-',
        icon: menu.icon || '-',
        parent: menu.parent ? menu.parent.title : '-',
        order_index: menu.order_index,
        is_active: menu.is_active,
        is_title: menu.is_title,
        is_divider: menu.is_divider,
        badge_text: menu.badge_text || '-',
        table_name: menu.table ? menu.table.display_name : '-',
        children_count: menus.filter(m => m.parent_id === menu.id).length,
        created_at: menu.created_at
      }));

      res.json({
        draw: parseInt(req.query.draw) || 1,
        recordsTotal: formattedMenus.length,
        recordsFiltered: formattedMenus.length,
        data: formattedMenus
      });
    } catch (error) {
      console.error('Error getting menus data:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Lấy menu theo ID
  getMenu: async (req, res) => {
    try {
      const { id } = req.params;
      const menu = await menuService.getMenuById(id);
      
      if (!menu) {
        return res.status(404).json({ success: false, message: 'Menu not found' });
      }

      res.json({ success: true, data: menu });
    } catch (error) {
      console.error('Error getting menu:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Tạo menu mới
  createMenu: async (req, res) => {
    try {
      const menuData = processMenuData(req.body);
      const menu = await menuService.createMenu(menuData);
      res.status(201).json({ success: true, data: menu, message: 'Menu created successfully' });
    } catch (error) {
      console.error('Error creating menu:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Cập nhật menu
  updateMenu: async (req, res) => {
    try {
      const { id } = req.params;
      const menuData = processMenuData(req.body);
      const menu = await menuService.updateMenu(id, menuData);
      res.json({ success: true, data: menu, message: 'Menu updated successfully' });
    } catch (error) {
      console.error('Error updating menu:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Xóa menu
  deleteMenu: async (req, res) => {
    try {
      const { id } = req.params;
      await menuService.deleteMenu(id);
      res.json({ success: true, message: 'Menu deleted successfully' });
    } catch (error) {
      console.error('Error deleting menu:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Đồng bộ menu từ tables
  syncTableMenus: async (req, res) => {
    try {
      const result = await menuService.syncTableMenus();
      res.json({ success: true, message: result.message });
    } catch (error) {
      console.error('Error syncing table menus:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Cập nhật thứ tự menu
  updateMenuOrder: async (req, res) => {
    try {
      const { menuOrders } = req.body;
      const result = await menuService.updateMenuOrder(menuOrders);
      res.json({ success: true, message: result.message });
    } catch (error) {
      console.error('Error updating menu order:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // Tạo menu mặc định
  createDefaultMenus: async (req, res) => {
    try {
      const result = await menuService.createDefaultMenus();
      res.json({ success: true, message: result.message });
    } catch (error) {
      console.error('Error creating default menus:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // API lấy menu cho sidebar
  getSidebarMenus: async (req, res) => {
    try {
      const menus = await menuService.getAllMenus();
      res.json({ success: true, data: menus });
    } catch (error) {
      console.error('Error getting sidebar menus:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // API lấy danh sách parent menus cho dropdown
  getParentMenus: async (req, res) => {
    try {
      const menus = await menuService.getFlatMenuList();
      const parentMenus = menus.filter(menu => !menu.is_divider && !menu.table_id);
      
      res.json({ 
        success: true, 
        data: parentMenus.map(menu => ({
          id: menu.id,
          title: menu.title,
          level: menu.parent_id ? 1 : 0
        }))
      });
    } catch (error) {
      console.error('Error getting parent menus:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  },

  // API lấy danh sách admin tables cho dropdown
  getAdminTables: async (req, res) => {
    try {
      const tables = await adminService.getAllAdminTables();
      
      res.json({ 
        success: true, 
        data: tables.map(table => ({
          id: table.id,
          name: table.name,
          display_name: table.display_name
        }))
      });
    } catch (error) {
      console.error('Error getting admin tables:', error);
      res.status(500).json({ success: false, message: error.message });
    }
  }
};

// Helper function để xử lý dữ liệu menu từ form
function processMenuData(rawData) {
  const processedData = { ...rawData };

  // Convert boolean fields
  const booleanFields = ['is_active', 'is_title', 'is_divider'];
  booleanFields.forEach(field => {
    if (processedData[field] !== undefined) {
      // Convert string 'true'/'false', 'on'/undefined, hoặc boolean values
      processedData[field] = processedData[field] === true ||
                            processedData[field] === 'true' ||
                            processedData[field] === 'on';
    }
  });

  // Convert numeric fields
  const numericFields = ['parent_id', 'order_index', 'table_id'];
  numericFields.forEach(field => {
    if (processedData[field] !== undefined && processedData[field] !== '') {
      const numValue = parseInt(processedData[field]);
      processedData[field] = isNaN(numValue) ? null : numValue;
    } else {
      processedData[field] = null;
    }
  });

  // Clean up empty string values
  Object.keys(processedData).forEach(key => {
    if (processedData[key] === '') {
      processedData[key] = null;
    }
  });

  return processedData;
}

module.exports = menuController;
